#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试行映射问题 - 找出真正的行边界
"""

import pandas as pd
import tabula

def debug_row_mapping():
    """调试行映射问题"""
    pdf_path = "../files/8-bob-623136717-BOB-STATEMENT-page-1-2.pdf"
    
    print('=' * 80)
    print('调试行映射问题 - 找出真正的行边界')
    print('=' * 80)
    
    try:
        dfs = tabula.read_pdf(pdf_path, pages='1', 
                            stream=True,
                            pandas_options={'header': None, 'dtype': str})
        
        if dfs:
            df = dfs[0]
            print(f"原始表格形状: {df.shape}")
            
            # 找到表头
            header_idx = 0
            data_start = 2  # 跳过表头和子表头
            
            print(f"\n🔍 根据图片分析正确的行映射:")
            print("图片显示的正确结构:")
            print("  Serial 1: Opening Balance")
            print("  Serial 2: UPI/201921164575/20:37:38/UPI/bharatpe.905000575")
            print("  Serial 3: d + UPI/130515257828/15:09:48/UPI/adilshaikh8735@okh")
            print("  Serial 4: ym + UPI/130658724392/11:17:45/UPI/7046825565@ybl/Pa")
            
            print(f"\n📊 实际tabula提取的行结构:")
            for i in range(data_start, min(data_start + 15, len(df))):
                if i < len(df):
                    row = df.iloc[i]
                    serial = str(row.iloc[0]).strip()
                    desc = str(row.iloc[3]).strip() if len(row) > 3 else ""
                    
                    print(f"  行{i}: Serial='{serial}', Description='{desc}'")
            
            print(f"\n💡 关键发现:")
            print("根据tabula提取的结果，正确的映射应该是:")
            
            # 重新分析正确的映射
            correct_mapping = {
                1: {"desc_row": 2, "desc": "Opening Balance"},  # Serial 1在行2，描述也在行2
                2: {"desc_row": 3, "desc": "UPI/201921164575/20:37:38/UPI/bharatpe.905000575"},  # Serial 2的描述在行3
                3: {"desc_row": 6, "desc": "UPI/130515257828/15:09:48/UPI/adilshaikh8735@okh"},  # Serial 3的描述在行6
                4: {"desc_row": 9, "desc": "UPI/130658724392/11:17:45/UPI/7046825565@ybl/Pa"},  # Serial 4的描述在行9
            }
            
            for serial, info in correct_mapping.items():
                desc_row_idx = info["desc_row"]
                expected_desc = info["desc"]
                if desc_row_idx < len(df):
                    actual_desc = str(df.iloc[desc_row_idx].iloc[3]).strip()
                    print(f"  Serial {serial}: 期望描述='{expected_desc}', 实际行{desc_row_idx}='{actual_desc}'")
                    
                    # 检查是否匹配
                    if expected_desc in actual_desc:
                        print(f"    ✅ 匹配！")
                    else:
                        print(f"    ❌ 不匹配！")
            
            print(f"\n🔧 修复策略:")
            print("问题：当前算法错误地将Serial N+1的描述合并到Serial N")
            print("解决：每个Serial应该只收集自己对应的描述行，不要收集到下一个Serial之前的所有行")
    
    except Exception as e:
        print(f"❌ 调试失败: {e}")

if __name__ == "__main__":
    debug_row_mapping()
