#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终调试 - 精确对比图片和实际结果
"""

import pandas as pd
import tabula

def final_debug():
    """最终调试"""
    pdf_path = "../files/8-bob-623136717-BOB-STATEMENT-page-1-2.pdf"
    
    print('=' * 80)
    print('最终调试 - 精确对比图片和实际结果')
    print('=' * 80)
    
    try:
        dfs = tabula.read_pdf(pdf_path, pages='1', 
                            stream=True,
                            pandas_options={'header': None, 'dtype': str})
        
        if dfs:
            df = dfs[0]
            
            print(f"图片中的正确结构:")
            print("  Serial 1: Opening Balance")
            print("  Serial 2: UPI/201921164575/20:37:38/UPI/bharatpe.905000575")
            print("  Serial 3: d + UPI/130515257828/15:09:48/UPI/adilshaikh8735@okh")
            print("  Serial 4: ym + UPI/130658724392/11:17:45/UPI/7046825565@ybl/Pa")
            
            print(f"\n当前解析结果:")
            print("  Serial 1: Opening Balance ✅")
            print("  Serial 2: UPI/201921164575/20:37:38/UPI/bharatpe.905000575 ✅")
            print("  Serial 3: 2 UPI/130515257828/15:09:48/UPI/adilshaikh8735@okh ❌ (应该是d)")
            print("  Serial 4: d UPI/130658724392/11:17:45/UPI/7046825565@ybl/Pa ❌ (应该是ym)")
            
            print(f"\n详细行分析:")
            data_start = 2
            for i in range(data_start, min(data_start + 15, len(df))):
                if i < len(df):
                    row = df.iloc[i]
                    serial = str(row.iloc[0]).strip()
                    desc = str(row.iloc[3]).strip() if len(row) > 3 else ""
                    
                    if serial.isdigit():
                        print(f"  行{i}: Serial {serial} - 主行")
                    elif desc and desc.lower() != 'nan':
                        print(f"  行{i}: 描述行 - '{desc}'")
            
            print(f"\n🔍 关键发现:")
            print("问题：Serial 3收集了Serial 2的描述部分")
            print("解决：需要更精确的边界识别")
    
    except Exception as e:
        print(f"❌ 调试失败: {e}")

if __name__ == "__main__":
    final_debug()
