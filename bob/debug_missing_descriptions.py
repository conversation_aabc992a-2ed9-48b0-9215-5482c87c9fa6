#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试Serial 11和13描述缺失问题
"""

import pandas as pd
import tabula

def debug_missing_descriptions():
    """调试描述缺失问题"""
    pdf_path = "../files/8-bob-623136717-BOB-STATEMENT-page-1-2.pdf"
    
    print('=' * 80)
    print('调试Serial 11和13描述缺失问题')
    print('=' * 80)
    
    try:
        # 解析第1页
        dfs = tabula.read_pdf(pdf_path, pages='1', 
                            stream=True,
                            pandas_options={'header': None, 'dtype': str})
        
        if dfs:
            df = dfs[0]
            print(f"第1页原始表格形状: {df.shape}")
            
            # 找到所有Serial行和描述行
            serial_positions = []
            description_rows = []
            
            for i, row in df.iterrows():
                serial_no = str(row.iloc[0]).strip() if len(row) > 0 else ""
                desc = str(row.iloc[3]).strip() if len(row) > 3 else ""
                
                if serial_no and serial_no.lower() != 'nan' and serial_no.isdigit():
                    serial_positions.append((i, int(serial_no), row))
                elif desc and desc.lower() != 'nan':
                    description_rows.append((i, desc))
            
            print(f"\n📍 找到 {len(serial_positions)} 个Serial记录")
            print(f"📍 找到 {len(description_rows)} 个描述行")
            
            # 重点分析Serial 11和13
            target_serials = [11, 13]
            
            for target_serial in target_serials:
                print(f"\n" + "="*60)
                print(f"🔍 详细分析Serial {target_serial}")
                print(f"="*60)
                
                # 找到目标Serial的位置
                target_pos = None
                target_row = None
                for pos, serial_num, row in serial_positions:
                    if serial_num == target_serial:
                        target_pos = pos
                        target_row = row
                        break
                
                if target_pos is not None:
                    print(f"📍 Serial {target_serial}位置: 行{target_pos}")
                    print(f"📋 Serial {target_serial}行内容:")
                    for col_idx, value in enumerate(target_row):
                        print(f"    列{col_idx}: '{str(value).strip()}'")
                    
                    # 检查Serial行本身是否有描述
                    serial_desc = str(target_row.iloc[3]).strip() if len(target_row) > 3 else ""
                    if serial_desc and serial_desc.lower() != 'nan':
                        print(f"✅ Serial {target_serial}行本身有描述: '{serial_desc}'")
                    else:
                        print(f"❌ Serial {target_serial}行本身无描述")
                    
                    # 分析前后相邻行（扩大范围到±5行）
                    print(f"\n🔍 Serial {target_serial}前后相邻行分析（±5行）:")
                    start_range = max(0, target_pos - 5)
                    end_range = min(len(df), target_pos + 6)
                    
                    for i in range(start_range, end_range):
                        if i < len(df):
                            row = df.iloc[i]
                            serial = str(row.iloc[0]).strip() if len(row) > 0 else ""
                            desc = str(row.iloc[3]).strip() if len(row) > 3 else ""
                            
                            marker = "👉" if i == target_pos else "  "
                            serial_info = f"Serial={serial}" if serial != 'nan' else "Serial=nan"
                            desc_info = f"Desc='{desc}'" if desc != 'nan' else "Desc=nan"
                            
                            print(f"    {marker} 行{i}: {serial_info}, {desc_info}")
                    
                    # 查找可能属于该Serial的描述
                    print(f"\n🔍 查找可能属于Serial {target_serial}的描述:")
                    potential_descriptions = []
                    
                    # 在前后5行范围内查找描述
                    for i in range(start_range, end_range):
                        if i != target_pos and i < len(df):
                            row = df.iloc[i]
                            desc = str(row.iloc[3]).strip() if len(row) > 3 else ""
                            if desc and desc.lower() != 'nan':
                                distance = abs(i - target_pos)
                                potential_descriptions.append((i, desc, distance))
                    
                    if potential_descriptions:
                        print(f"    找到 {len(potential_descriptions)} 个潜在描述:")
                        for pos, desc, distance in sorted(potential_descriptions, key=lambda x: x[2]):
                            print(f"      行{pos} (距离{distance}): '{desc}'")
                    else:
                        print(f"    ❌ 未找到任何潜在描述")
                    
                    # 分析为什么当前算法没有收集到这些描述
                    print(f"\n🤔 算法分析:")
                    print(f"    当前智能算法的选择逻辑:")
                    print(f"    1. 检查Serial行本身是否有描述")
                    print(f"    2. 如果没有，在附近3行范围内查找UPI信息和短字符")
                    print(f"    3. 根据内容特征进行智能匹配")
                    
                    # 模拟当前算法的逻辑
                    nearby_descriptions = []
                    for pos, desc in description_rows:
                        if abs(pos - target_pos) <= 3:
                            nearby_descriptions.append((pos, desc))
                    
                    if nearby_descriptions:
                        print(f"    算法范围内(±3行)的描述: {len(nearby_descriptions)}个")
                        for pos, desc in nearby_descriptions:
                            print(f"      行{pos}: '{desc}'")
                        
                        # 分类描述
                        upi_descriptions = [(pos, desc) for pos, desc in nearby_descriptions if 'UPI/' in desc and len(desc) > 20]
                        short_descriptions = [(pos, desc) for pos, desc in nearby_descriptions if len(desc) <= 3]
                        
                        print(f"    UPI描述: {len(upi_descriptions)}个")
                        print(f"    短字符描述: {len(short_descriptions)}个")
                        
                        if not upi_descriptions and not short_descriptions:
                            print(f"    ❌ 算法判断：无有效描述内容")
                        else:
                            print(f"    ✅ 算法应该能找到描述，可能存在bug")
                    else:
                        print(f"    ❌ 算法范围内(±3行)无描述内容")
                        print(f"    💡 建议：可能需要扩大搜索范围或调整匹配逻辑")
                
                else:
                    print(f"❌ 未找到Serial {target_serial}")
    
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_missing_descriptions()
