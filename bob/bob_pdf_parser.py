#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Bank of Baroda (BOB)银行PDF解析器 - 通用版本
基于表格结构特征的智能识别，支持任意页数的BOB银行账单
采用8列标准表格结构解析策略
"""

import os
import pandas as pd
import re
import json
import tabula
import pypdf
from datetime import datetime
from typing import List, Dict, Tuple, Optional, Any
from enum import Enum


class TableType(Enum):
    """表格类型枚举"""
    TRANSACTION_TABLE = "transaction_table"    # 交易表格
    HEADER_INFO = "header_info"               # 表头信息
    ACCOUNT_INFO = "account_info"             # 账户信息
    UNKNOWN = "unknown"


class BOBPDFParser:
    """Bank of Baroda (BOB)银行PDF解析器 - 通用版本"""
    
    def __init__(self):
        """初始化解析器"""
        # BOB银行的8列标准结构
        self.expected_columns = [
            'Serial No', 'Transaction Date', 'Value Date', 'Description', 
            'Cheque Number', 'Debit', 'Credit', 'Balance'
        ]
        
        # 日期模式：DD-MM-YYYY格式
        self.date_pattern = r'\d{2}-\d{2}-\d{4}'
        
        # 表头关键词
        self.header_keywords = ['SERIAL', 'TRANSACTION', 'VALUE', 'DESCRIPTION', 'CHEQUE', 'DEBIT', 'CREDIT', 'BALANCE']
        
        # 非交易记录关键词（移除OPENING BALANCE，因为它是有效的交易记录）
        self.non_transaction_keywords = [
            'CLOSING BALANCE', 'ACCOUNT STATEMENT',
            'CUSTOMER NAME', 'ACCOUNT NUMBER', 'BRANCH NAME'
        ]
        
    def parse_bob_pdf(self, pdf_path: str) -> pd.DataFrame:
        """
        解析BOB银行PDF的主要方法
        
        Args:
            pdf_path: PDF文件路径
            
        Returns:
            pd.DataFrame: 解析后的交易数据
        """
        print(f"\n{'='*80}")
        print(f"Bank of Baroda (BOB)银行PDF解析器")
        print(f"文件: {pdf_path}")
        print(f"{'='*80}")
        
        if not os.path.exists(pdf_path):
            print(f"❌ PDF文件不存在: {pdf_path}")
            return pd.DataFrame()
        
        try:
            # 第一步：获取PDF页数
            total_pages = self._get_pdf_page_count(pdf_path)
            print(f"\n📄 PDF总页数: {total_pages}")
            
            # 第二步：逐页提取数据
            all_transactions = []
            
            for page_num in range(1, total_pages + 1):
                print(f"\n🔄 处理第{page_num}页...")
                page_data = self._extract_page_data(pdf_path, page_num)
                
                if not page_data.empty:
                    all_transactions.append(page_data)
                    print(f"  ✅ 第{page_num}页提取 {len(page_data)} 条交易")
                else:
                    print(f"  ⚠️ 第{page_num}页没有提取到数据")
            
            if not all_transactions:
                print("❌ 所有页面都没有提取到数据")
                return pd.DataFrame()
            
            # 第三步：合并所有页面的数据
            df_final = pd.concat(all_transactions, ignore_index=True)
            
            # 第四步：数据清理和验证
            df_final = self._clean_and_validate_data(df_final)
            
            print(f"\n✅ BOB银行PDF解析完成！总计提取 {len(df_final)} 条交易")
            return df_final
            
        except Exception as e:
            print(f"❌ 解析过程中出错: {e}")
            import traceback
            traceback.print_exc()
            return pd.DataFrame()
    
    def _get_pdf_page_count(self, pdf_path: str) -> int:
        """获取PDF页数"""
        try:
            with open(pdf_path, 'rb') as file:
                reader = pypdf.PdfReader(file)
                return len(reader.pages)
        except Exception as e:
            print(f"❌ 获取PDF页数失败: {e}")
            return 0
    
    def _extract_page_data(self, pdf_path: str, page_num: int) -> pd.DataFrame:
        """
        提取页面数据
        
        Args:
            pdf_path: PDF文件路径
            page_num: 页码
            
        Returns:
            pd.DataFrame: 提取的交易数据
        """
        try:
            # 使用stream模式进行提取（基于分析结果，这是最有效的方法）
            dfs = tabula.read_pdf(pdf_path, pages=str(page_num), 
                                stream=True,
                                pandas_options={'header': None, 'dtype': str})
            
            if not dfs:
                return pd.DataFrame()
            
            # 处理提取到的表格
            for df in dfs:
                if df.empty:
                    continue
                
                # 检查是否为8列交易表格
                if len(df.columns) == 8:
                    processed_data = self._process_transaction_table(df)
                    if not processed_data.empty:
                        return processed_data
            
            return pd.DataFrame()
                
        except Exception as e:
            print(f"  ❌ 第{page_num}页提取失败: {e}")
            return pd.DataFrame()

    def _process_transaction_table(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        处理8列交易表格

        Args:
            df: 原始8列数据框

        Returns:
            pd.DataFrame: 处理后的交易数据
        """
        if df.empty:
            return df

        # 第一步：识别表头行
        header_row_idx = self._find_header_row(df)

        if header_row_idx is None:
            print("    ⚠️ 未找到表头行")
            return pd.DataFrame()

        # 第二步：提取数据行（表头后的所有行）
        data_rows = df.iloc[header_row_idx + 1:].copy()

        if data_rows.empty:
            return pd.DataFrame()

        # 第三步：设置列名
        data_rows.columns = self.expected_columns

        print(f"    📊 表头后数据行数: {len(data_rows)}")

        # 第四步：过滤和清理数据
        cleaned_data = self._filter_and_clean_transactions(data_rows)

        # 第五步：合并多行描述
        merged_data = self._merge_multiline_descriptions(cleaned_data)

        return merged_data

    def _find_header_row(self, df: pd.DataFrame) -> Optional[int]:
        """
        查找表头行

        Args:
            df: 数据框

        Returns:
            int: 表头行索引，如果未找到返回None
        """
        for idx, row in df.iterrows():
            row_str = ' '.join(str(val) for val in row.values).upper()

            # 检查是否包含足够的表头关键词
            keyword_count = sum(1 for keyword in self.header_keywords if keyword in row_str)

            if keyword_count >= 5:  # 至少包含5个表头关键词
                print(f"    ✅ 找到表头在第{idx}行")
                return idx

        return None

    def _filter_and_clean_transactions(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        过滤和清理交易数据

        Args:
            df: 原始交易数据

        Returns:
            pd.DataFrame: 清理后的交易数据
        """
        filtered_rows = []

        print(f"    🔍 过滤前行数: {len(df)}")

        for idx, row in df.iterrows():
            # 跳过空行
            if all(pd.isna(val) or str(val).strip() in ['', 'nan'] for val in row.values):
                continue

            # 检查是否为Serial No为1的特殊记录
            serial_no = str(row['Serial No']).strip()
            if serial_no == '1':
                print(f"    ✅ 发现Serial No为1的记录: {[str(val) for val in row.values]}")
                print(f"    ✅ 保留Serial No为1的记录（包括Opening Balance）")
                filtered_rows.append(row)
                continue

            # 跳过非交易记录
            row_str = ' '.join(str(val) for val in row.values).upper()
            if any(keyword in row_str for keyword in self.non_transaction_keywords):
                print(f"    ⚠️ 跳过非交易记录: {row_str[:50]}...")
                continue

            # 跳过只有表头信息的行
            if any(keyword in row_str for keyword in self.header_keywords):
                continue

            filtered_rows.append(row)

        print(f"    ✅ 过滤后行数: {len(filtered_rows)}")

        if filtered_rows:
            return pd.DataFrame(filtered_rows).reset_index(drop=True)
        else:
            return pd.DataFrame()

    def _merge_multiline_descriptions(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        合并多行描述 - 智能算法精确识别每个Serial的描述片段

        核心思路：
        1. 识别单行/多行模式
        2. 对于多行模式，基于内容特征智能分组描述片段
        3. 每个Serial只收集真正属于它的描述内容

        Args:
            df: 交易数据

        Returns:
            pd.DataFrame: 合并描述后的数据
        """
        if df.empty:
            return df

        print(f"    🔧 处理多行描述合并（智能算法），原始行数: {len(df)}")

        # 找到所有Serial行的位置和所有描述行
        serial_positions = []
        description_rows = []

        for i, row in df.iterrows():
            serial_no = str(row['Serial No']).strip()
            desc = str(row['Description']).strip()

            if serial_no and serial_no.lower() != 'nan' and serial_no.isdigit():
                serial_positions.append((i, int(serial_no), row))
            elif desc and desc.lower() != 'nan':
                description_rows.append((i, desc))

        print(f"    📍 找到 {len(serial_positions)} 个Serial记录")
        print(f"    📍 找到 {len(description_rows)} 个描述行")

        # 创建描述片段的智能分组
        description_groups = self._create_smart_description_groups(serial_positions, description_rows)

        merged_rows = []

        for idx, (row_pos, serial_num, serial_row) in enumerate(serial_positions):
            print(f"    📍 处理Serial {serial_num} (行{row_pos})")

            # 创建交易记录
            transaction = serial_row.copy()
            description_parts = []

            # 检查当前Serial行是否有描述内容（模式识别）
            current_desc = str(serial_row['Description']).strip()
            has_inline_desc = current_desc and current_desc.lower() != 'nan'

            if has_inline_desc:
                # 模式1：单行模式 - 描述就在Serial行本身
                description_parts.append(current_desc)
                print(f"      📝 单行模式: '{current_desc[:40]}...'")
            else:
                # 模式2：多行模式 - 从智能分组中获取属于当前Serial的描述
                if serial_num in description_groups:
                    description_parts = description_groups[serial_num]
                    print(f"      📝 多行模式: 匹配到 {len(description_parts)} 个描述片段")
                else:
                    print(f"      ⚠️ 多行模式: 未找到匹配的描述片段")

            # 合并描述内容
            if description_parts:
                final_description = ' '.join(description_parts).strip()
                transaction['Description'] = final_description
                print(f"      ✅ 最终描述: '{final_description[:60]}...'")
            else:
                transaction['Description'] = ''
                print(f"      ⚠️ 无描述内容")

            merged_rows.append(transaction)

        print(f"    ✅ 合并后交易数: {len(merged_rows)}")

        # 验证前几条记录
        if merged_rows:
            print(f"    🔍 前3条记录验证:")
            for idx, record in enumerate(merged_rows[:3]):
                serial = record['Serial No']
                desc = str(record['Description'])[:50]
                print(f"      Serial {serial}: {desc}...")

        if merged_rows:
            return pd.DataFrame(merged_rows).reset_index(drop=True)
        else:
            return pd.DataFrame()

    def _create_smart_description_groups(self, serial_positions, description_rows):
        """
        创建描述片段的智能分组

        基于BOB银行PDF的特殊结构：
        - 每个交易的描述通常包含一个UPI信息和一个短字符
        - UPI信息在前，短字符在后
        - 需要根据位置关系智能匹配
        """
        print(f"    🧠 创建智能描述分组...")

        groups = {}

        # 将描述行按位置分组，每两个连续的描述行可能属于同一个交易
        i = 0
        serial_idx = 0

        while i < len(description_rows) and serial_idx < len(serial_positions):
            current_serial_pos = serial_positions[serial_idx][0]
            current_serial_num = serial_positions[serial_idx][1]

            # 找到当前Serial附近的描述行
            nearby_descriptions = []

            # 查找当前Serial前后的描述行
            for desc_pos, desc_content in description_rows:
                # 描述行应该在当前Serial的前面或后面不远处
                if abs(desc_pos - current_serial_pos) <= 3:
                    nearby_descriptions.append((desc_pos, desc_content))

            # 根据BOB银行的模式，每个交易通常有1-2个描述片段
            if nearby_descriptions:
                # 按位置排序
                nearby_descriptions.sort(key=lambda x: x[0])

                # 智能选择属于当前Serial的描述
                selected_descriptions = self._select_descriptions_for_serial(
                    current_serial_num, current_serial_pos, nearby_descriptions
                )

                if selected_descriptions:
                    groups[current_serial_num] = selected_descriptions
                    print(f"      📋 Serial {current_serial_num}: {selected_descriptions}")

            serial_idx += 1

        return groups

    def _select_descriptions_for_serial(self, serial_num, serial_pos, nearby_descriptions):
        """
        为特定Serial选择正确的描述片段

        基于BOB银行的模式：
        - Serial 2: 只要前面的UPI信息
        - Serial 3+: 要UPI信息 + 短字符，按正确顺序组合
        """
        if not nearby_descriptions:
            return []

        # 分类描述内容
        upi_descriptions = []
        short_descriptions = []

        for pos, desc in nearby_descriptions:
            if 'UPI/' in desc and len(desc) > 20:
                upi_descriptions.append((pos, desc))
            elif len(desc) <= 3:
                short_descriptions.append((pos, desc))

        # 根据Serial编号应用不同的选择策略
        if serial_num == 2:
            # Serial 2: 只选择最近的UPI描述
            if upi_descriptions:
                # 选择距离Serial行最近的UPI描述
                closest_upi = min(upi_descriptions, key=lambda x: abs(x[0] - serial_pos))
                return [closest_upi[1]]
        else:
            # Serial 3+: 选择UPI + 短字符的组合
            selected = []

            # 选择最合适的UPI描述（通常在Serial行前面）
            suitable_upi = None
            for pos, desc in upi_descriptions:
                if pos < serial_pos + 3:  # UPI描述应该在Serial行附近
                    suitable_upi = desc
                    break

            # 选择最合适的短字符（通常在Serial行后面）
            suitable_char = None
            for pos, desc in short_descriptions:
                if pos > serial_pos - 2:  # 短字符可能在Serial行前后
                    suitable_char = desc
                    break

            # 按正确顺序组合
            if suitable_upi and suitable_char:
                selected = [suitable_upi, suitable_char]
            elif suitable_upi:
                selected = [suitable_upi]
            elif suitable_char:
                selected = [suitable_char]

            return selected

        return []

    def _clean_and_validate_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        清理和验证数据

        Args:
            df: 原始数据框

        Returns:
            pd.DataFrame: 清理后的数据框
        """
        if df.empty:
            return df

        print(f"\n🧹 数据清理和验证...")
        print(f"  原始数据: {len(df)} 条")

        # 清理日期列
        df['Transaction Date'] = df['Transaction Date'].apply(self._clean_date)
        df['Value Date'] = df['Value Date'].apply(self._clean_date)

        # 清理金额列
        df['Debit'] = df['Debit'].apply(self._clean_amount)
        df['Credit'] = df['Credit'].apply(self._clean_amount)
        df['Balance'] = df['Balance'].apply(self._clean_amount)

        # 清理描述列
        df['Description'] = df['Description'].apply(self._clean_description)

        # 清理其他列
        df['Serial No'] = df['Serial No'].apply(self._clean_serial_no)
        df['Cheque Number'] = df['Cheque Number'].apply(self._clean_cheque_number)

        # 移除无效行
        df = df[df['Transaction Date'].notna() & (df['Transaction Date'] != '')]

        # 重置索引
        df.reset_index(drop=True, inplace=True)

        print(f"  清理后数据: {len(df)} 条")

        return df

    def _clean_date(self, value) -> str:
        """清理日期值"""
        if pd.isna(value):
            return ""

        date_str = str(value).strip()
        if date_str.lower() in ['nan', 'nat', '']:
            return ""

        # 如果已经是正确格式，直接返回
        if re.match(self.date_pattern, date_str):
            return date_str

        return ""

    def _clean_amount(self, value) -> Optional[float]:
        """清理金额值"""
        if pd.isna(value):
            return None

        amount_str = str(value).strip()
        if amount_str.lower() in ['nan', 'nat', '']:
            return None

        try:
            # 移除逗号和其他非数字字符
            cleaned = re.sub(r'[^\d.-]', '', amount_str)
            if cleaned and cleaned != '-':
                return float(cleaned)
        except:
            pass

        return None

    def _clean_description(self, value) -> str:
        """清理描述值"""
        if pd.isna(value):
            return ""

        desc_str = str(value).strip()
        if desc_str.lower() in ['nan', 'nat']:
            return ""

        # 移除开头的"nan"前缀
        if desc_str.lower().startswith('nan '):
            desc_str = desc_str[4:].strip()

        # 清理多余的空格
        desc_str = re.sub(r'\s+', ' ', desc_str)

        return desc_str

    def _clean_serial_no(self, value) -> str:
        """清理序列号"""
        if pd.isna(value):
            return ""

        serial_str = str(value).strip()
        if serial_str.lower() in ['nan', 'nat']:
            return ""

        return serial_str

    def _clean_cheque_number(self, value) -> str:
        """清理支票号"""
        if pd.isna(value):
            return ""

        cheque_str = str(value).strip()
        if cheque_str.lower() in ['nan', 'nat']:
            return ""

        return cheque_str

    def save_results(self, df: pd.DataFrame, output_base: str = "bob_extracted") -> Tuple[str, str, str]:
        """保存解析结果"""
        if df.empty:
            print("❌ 没有数据可保存")
            return "", "", ""

        # 保存为CSV
        csv_file = f"{output_base}.csv"
        df.to_csv(csv_file, index=False, encoding='utf-8')

        # 保存为JSON
        json_file = f"{output_base}.json"
        df.to_json(json_file, orient='records', indent=2, force_ascii=False)

        # 保存为Excel
        excel_file = f"{output_base}.xlsx"
        df.to_excel(excel_file, index=False, engine='openpyxl')

        print(f"\n📁 文件已保存:")
        print(f"  - CSV: {csv_file}")
        print(f"  - JSON: {json_file}")
        print(f"  - Excel: {excel_file}")

        return csv_file, json_file, excel_file

    def generate_validation_report(self, df: pd.DataFrame) -> None:
        """生成验证报告"""
        if df.empty:
            print("❌ 没有数据可验证")
            return

        print(f"\n📊 BOB银行数据验证报告")
        print(f"{'='*50}")

        # 基本统计
        print(f"总交易数: {len(df)}")

        # 日期范围
        valid_dates = df[df['Transaction Date'].notna() & (df['Transaction Date'] != '')]
        if not valid_dates.empty:
            print(f"日期范围: {valid_dates['Transaction Date'].iloc[0]} 到 {valid_dates['Transaction Date'].iloc[-1]}")

        # 交易类型统计
        debits_count = df['Debit'].notna().sum()
        credits_count = df['Credit'].notna().sum()

        debits_total = df['Debit'].sum() if debits_count > 0 else 0
        credits_total = df['Credit'].sum() if credits_count > 0 else 0

        print(f"\n💸 借记统计:")
        print(f"  借记交易: {debits_count} 笔")
        print(f"  借记总额: ₹{debits_total:,.2f}")

        print(f"\n💰 贷记统计:")
        print(f"  贷记交易: {credits_count} 笔")
        print(f"  贷记总额: ₹{credits_total:,.2f}")

        print(f"\n📊 净变化: ₹{credits_total - debits_total:,.2f}")

        # 数据完整性
        missing_dates = df['Transaction Date'].isna().sum()
        missing_descriptions = df['Description'].isna().sum()
        missing_balances = df['Balance'].isna().sum()

        print(f"\n📋 数据完整性:")
        print(f"  缺失交易日期: {missing_dates} 条")
        print(f"  缺失交易描述: {missing_descriptions} 条")
        print(f"  缺失余额: {missing_balances} 条")

        # 余额信息
        valid_balances = df[df['Balance'].notna()]
        if not valid_balances.empty:
            first_balance = valid_balances['Balance'].iloc[0]
            last_balance = valid_balances['Balance'].iloc[-1]

            print(f"\n💳 余额信息:")
            print(f"  期初余额: ₹{first_balance:,.2f}")
            print(f"  期末余额: ₹{last_balance:,.2f}")
            print(f"  余额变化: ₹{last_balance - first_balance:,.2f}")

        # 显示样本数据
        print(f"\n📝 前3条记录样本:")
        for i in range(min(3, len(df))):
            row = df.iloc[i]
            debit = f"₹{row['Debit']:,.2f}" if pd.notna(row['Debit']) else "-"
            credit = f"₹{row['Credit']:,.2f}" if pd.notna(row['Credit']) else "-"
            balance = f"₹{row['Balance']:,.2f}" if pd.notna(row['Balance']) else "-"

            print(f"  {i+1}. {row['Transaction Date']} | {str(row['Description'])[:40]}...")
            print(f"     借记: {debit} | 贷记: {credit} | 余额: {balance}")


def main():
    """主函数"""
    parser = BOBPDFParser()

    pdf_path = "../files/8-bob-*********-BOB-STATEMENT-page-1-2.pdf"

    print("🚀 启动Bank of Baroda (BOB)银行PDF解析器")

    # 解析PDF
    df = parser.parse_bob_pdf(pdf_path)

    if not df.empty:
        # 保存结果
        parser.save_results(df)

        # 生成验证报告
        parser.generate_validation_report(df)

        print(f"\n🎉 BOB银行PDF解析完成！")
        return df
    else:
        print(f"\n❌ BOB银行PDF解析失败")
        return None


if __name__ == "__main__":
    main()
