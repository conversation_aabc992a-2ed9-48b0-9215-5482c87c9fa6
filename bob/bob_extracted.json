[{"Serial No": "1", "Transaction Date": "01-06-2022", "Value Date": "01-06-2022", "Description": "Opening Balance", "Cheque Number": "", "Debit": null, "Credit": null, "Balance": 736280.0}, {"Serial No": "2", "Transaction Date": "01-06-2022", "Value Date": "01-06-2022", "Description": "UPI/201921164575/20:37:38/UPI/bharatpe.905000575", "Cheque Number": "", "Debit": null, "Credit": 138.0, "Balance": 736418.0}, {"Serial No": "3", "Transaction Date": "01-06-2022", "Value Date": "01-06-2022", "Description": "UPI/130515257828/15:09:48/UPI/adilshaikh8735@okh", "Cheque Number": "", "Debit": null, "Credit": 76000.0, "Balance": 812418.0}, {"Serial No": "4", "Transaction Date": "02-06-2022", "Value Date": "02-06-2022", "Description": "UPI/130658724392/11:17:45/UPI/7046825565@ybl/Pa ym", "Cheque Number": "", "Debit": null, "Credit": 20000.0, "Balance": 832418.0}, {"Serial No": "5", "Transaction Date": "03-06-2022", "Value Date": "03-06-2022", "Description": "UPI/130748035644/13:35:18/UPI/maulikchaudhari688 @", "Cheque Number": "", "Debit": null, "Credit": 12000.0, "Balance": 844418.0}, {"Serial No": "6", "Transaction Date": "04-06-2022", "Value Date": "04-06-2022", "Description": "UPI/132259332687/10:45:00/UPI/maulikchaudhari688 @", "Cheque Number": "", "Debit": null, "Credit": 62.0, "Balance": 844480.0}, {"Serial No": "7", "Transaction Date": "05-06-2022", "Value Date": "05-06-2022", "Description": "UPI/133193536972/18:08:28/UPI/maulikchaudhari688 @", "Cheque Number": "", "Debit": null, "Credit": 122.0, "Balance": 844602.0}, {"Serial No": "8", "Transaction Date": "05-06-2022", "Value Date": "05-06-2022", "Description": "UPI/134785681045/11:34:20/UPI/rnaraya<PERSON><PERSON>i@axl/P", "Cheque Number": "", "Debit": null, "Credit": 32000.0, "Balance": 876602.0}, {"Serial No": "9", "Transaction Date": "06-06-2022", "Value Date": "06-06-2022", "Description": "UPI/135090386568/11:22:41/UPI/maulikchaudhari688 @", "Cheque Number": "", "Debit": null, "Credit": 49.0, "Balance": 876651.0}, {"Serial No": "10", "Transaction Date": "06-06-2022", "Value Date": "06-06-2022", "Description": "UPI/135687277403/14:05:00/UPI/maulikchaudhari688 @", "Cheque Number": "", "Debit": null, "Credit": 130.0, "Balance": 876781.0}, {"Serial No": "11", "Transaction Date": "07-06-2022", "Value Date": "07-06-2022", "Description": "IMPS/P2A/200718269947/XXXXXXXXXX7220/ok", "Cheque Number": "", "Debit": 10.18, "Credit": null, "Balance": 876770.82}, {"Serial No": "12", "Transaction Date": "07-06-2022", "Value Date": "07-06-2022", "Description": "MBK/200740880173/18:34:43/surbhai", "Cheque Number": "", "Debit": 20000.0, "Credit": null, "Balance": 856770.82}, {"Serial No": "13", "Transaction Date": "07-06-2022", "Value Date": "07-06-2022", "Description": "IMPS/P2A/200718070142/XXXXXXXXXX7220/self", "Cheque Number": "", "Debit": 14001.77, "Credit": null, "Balance": 842769.05}, {"Serial No": "14", "Transaction Date": "09-06-2022", "Value Date": "09-06-2022", "Description": "MBK/201297503472/15:45:24/ok", "Cheque Number": "", "Debit": null, "Credit": 111.0, "Balance": 842880.05}, {"Serial No": "15", "Transaction Date": "09-06-2022", "Value Date": "09-06-2022", "Description": "MBK/201297613401/15:52:50/ok", "Cheque Number": "", "Debit": 111.0, "Credit": null, "Balance": 842769.05}, {"Serial No": "16", "Transaction Date": "09-06-2022", "Value Date": "09-06-2022", "Description": "SMSALERTFEE_-_01-07-2019_LIEN_REV", "Cheque Number": "", "Debit": 11.8, "Credit": null, "Balance": 842757.25}, {"Serial No": "17", "Transaction Date": "10-06-2022", "Value Date": "10-06-2022", "Description": "UPI/200894475092/12:12:36/UPI/jigardesai22336@ok", "Cheque Number": "", "Debit": 5000.0, "Credit": null, "Balance": 837757.25}, {"Serial No": "18", "Transaction Date": "10-06-2022", "Value Date": "10-06-2022", "Description": "UPI/200894486294/12:13:12/UPI/goog-", "Cheque Number": "", "Debit": null, "Credit": 6.0, "Balance": 837763.25}, {"Serial No": "19", "Transaction Date": "10-06-2022", "Value Date": "10-06-2022", "Description": "UPI/201035443395/08:34:41/UPI/gpay-", "Cheque Number": "", "Debit": 130.0, "Credit": null, "Balance": 837633.25}, {"Serial No": "20", "Transaction Date": "11-06-2022", "Value Date": "11-06-2022", "Description": "UPI/201154118832/08:26:18/UPI/gpay-", "Cheque Number": "", "Debit": 130.0, "Credit": null, "Balance": 837503.25}, {"Serial No": "21", "Transaction Date": "14-06-2022", "Value Date": "14-06-2022", "Description": "UPI/201272723906/08:24:54/UPI/gpay-", "Cheque Number": "", "Debit": 130.0, "Credit": null, "Balance": 837373.25}, {"Serial No": "22", "Transaction Date": "15-06-2022", "Value Date": "15-06-2022", "Description": "UPI/201275471204/11:20:04/UPI/paytmqr2810050501 01", "Cheque Number": "", "Debit": 450.0, "Credit": null, "Balance": 836923.25}, {"Serial No": "23", "Transaction Date": "16-06-2022", "Value Date": "16-06-2022", "Description": "UPI/201275652329/11:27:54/UPI/9725539266@axl/ok", "Cheque Number": "", "Debit": 20.0, "Credit": null, "Balance": 836903.25}, {"Serial No": "24", "Transaction Date": "16-06-2022", "Value Date": "16-06-2022", "Description": "UPI/201284918509/18:56:00/UPI/gajanan<PERSON>@ici", "Cheque Number": "", "Debit": 202.0, "Credit": null, "Balance": 836701.25}, {"Serial No": "25", "Transaction Date": "17-06-2022", "Value Date": "17-06-2022", "Description": "UPI/201288207814/21:11:15/UPI/bharatpe.905000575", "Cheque Number": "", "Debit": 26.0, "Credit": null, "Balance": 836675.25}, {"Serial No": "26", "Transaction Date": "18-06-2022", "Value Date": "18-06-2022", "Description": "UPI/201391239100/08:27:41/UPI/gpay-", "Cheque Number": "", "Debit": 130.0, "Credit": null, "Balance": 836545.25}, {"Serial No": "27", "Transaction Date": "19-06-2022", "Value Date": "19-06-2022", "Description": "UPI/201394483461/11:51:15/UPI/q86648939@ybl/dies", "Cheque Number": "", "Debit": 3156.63, "Credit": null, "Balance": 833388.62}, {"Serial No": "28", "Transaction Date": "21-06-2022", "Value Date": "21-06-2022", "Description": "UPI/201315559404/15:14:04/UPI/dhruvildixit2@oksbi", "Cheque Number": "", "Debit": null, "Credit": 4250.0, "Balance": 837638.62}, {"Serial No": "29", "Transaction Date": "22-06-2022", "Value Date": "22-06-2022", "Description": "UPI/201429635706/18:55:47/UPI/billdesk.electricit @", "Cheque Number": "", "Debit": 30.0, "Credit": null, "Balance": 837608.62}, {"Serial No": "30", "Transaction Date": "22-06-2022", "Value Date": "22-06-2022", "Description": "UPI/201538235609/12:14:27/UPI/patelkalpesh151987 @", "Cheque Number": "", "Debit": 200.0, "Credit": null, "Balance": 837408.62}, {"Serial No": "31", "Transaction Date": "23-06-2022", "Value Date": "23-06-2022", "Description": "UPI/201765231952/09:10:31/UPI/gpay-", "Cheque Number": "", "Debit": 130.0, "Credit": null, "Balance": 837278.62}, {"Serial No": "32", "Transaction Date": "23-06-2022", "Value Date": "23-06-2022", "Description": "UPI/201767152600/11:15:25/UPI/euronetgpay.pay@ici", "Cheque Number": "", "Debit": 179.0, "Credit": null, "Balance": 837099.62}, {"Serial No": "33", "Transaction Date": "23-06-2022", "Value Date": "23-06-2022", "Description": "UPI/201767327840/11:24:38/UPI/goog-", "Cheque Number": "", "Debit": null, "Credit": 6.0, "Balance": 837105.62}, {"Serial No": "34", "Transaction Date": "24-06-2022", "Value Date": "24-06-2022", "Description": "UPI/201770112024/13:50:10/UPI/q12325437@ybl/ok 01", "Cheque Number": "", "Debit": 120.0, "Credit": null, "Balance": 836985.62}, {"Serial No": "35", "Transaction Date": "25-06-2022", "Value Date": "25-06-2022", "Description": "UPI/201772703991/16:19:15/UPI/paytmqr2810050501 01", "Cheque Number": "", "Debit": 2000.0, "Credit": null, "Balance": 834985.62}, {"Serial No": "36", "Transaction Date": "26-06-2022", "Value Date": "26-06-2022", "Description": "UPI/201773630268/17:11:51/UPI/q393159212@ybl/bis", "Cheque Number": "", "Debit": 165.0, "Credit": null, "Balance": 834820.62}, {"Serial No": "37", "Transaction Date": "26-06-2022", "Value Date": "26-06-2022", "Description": "UPI/201778127611/20:40:53/UPI/q685539490@ybl/ok 01", "Cheque Number": "", "Debit": 27.0, "Credit": null, "Balance": 834793.62}, {"Serial No": "38", "Transaction Date": "27-06-2022", "Value Date": "27-06-2022", "Description": "UPI/201778177734/20:43:24/UPI/paytmqr2810050501 01", "Cheque Number": "", "Debit": 40.0, "Credit": null, "Balance": 834753.62}, {"Serial No": "39", "Transaction Date": "28-06-2022", "Value Date": "28-06-2022", "Description": "UPI/201881645738/08:53:56/UPI/gpay-", "Cheque Number": "", "Debit": 71.0, "Credit": null, "Balance": 834682.62}, {"Serial No": "40", "Transaction Date": "28-06-2022", "Value Date": "28-06-2022", "Description": "UPI/201885109016/12:25:43/UPI/paytmqr2810050501 01", "Cheque Number": "", "Debit": 540.0, "Credit": null, "Balance": 834142.62}, {"Serial No": "41", "Transaction Date": "28-06-2022", "Value Date": "28-06-2022", "Description": "UPI/201893226959/19:37:31/UPI/dhruvildixit2@oksbi", "Cheque Number": "", "Debit": 100.0, "Credit": null, "Balance": 834042.62}, {"Serial No": "42", "Transaction Date": "28-06-2022", "Value Date": "28-06-2022", "Description": "UPI/201819208824/19:39:17/UPI/dhruvildixit2@oksbi", "Cheque Number": "", "Debit": null, "Credit": 10000.0, "Balance": 844042.62}, {"Serial No": "43", "Transaction Date": "29-06-2022", "Value Date": "29-06-2022", "Description": "UPI/201893737887/19:59:36/UPI/dhruvildixit2@oksbi 01", "Cheque Number": "", "Debit": 100.0, "Credit": null, "Balance": 843942.62}, {"Serial No": "44", "Transaction Date": "30-06-2022", "Value Date": "30-06-2022", "Description": "UPI/201894302292/20:24:33/UPI/paytmqr2810050501 01", "Cheque Number": "", "Debit": 26.0, "Credit": null, "Balance": 843916.62}, {"Serial No": "45", "Transaction Date": "30-06-2022", "Value Date": "30-06-2022", "Description": "UPI/201894325804/20:25:40/UPI/goog-", "Cheque Number": "", "Debit": null, "Credit": 6.0, "Balance": 843922.62}, {"Serial No": "46", "Transaction Date": "01-07-2022", "Value Date": "01-07-2022", "Description": "BY CASH", "Cheque Number": "", "Debit": null, "Credit": 50000.0, "Balance": 893922.62}, {"Serial No": "47", "Transaction Date": "01-07-2022", "Value Date": "01-07-2022", "Description": "01560100026841:Int.Pd:01-01-2022 to 31-06-2022", "Cheque Number": "", "Debit": null, "Credit": 4589.0, "Balance": 898511.62}, {"Serial No": "48", "Transaction Date": "02-07-2022", "Value Date": "02-07-2022", "Description": "UPI/202024767518/08:49:36/UPI/gpay-", "Cheque Number": "", "Debit": 4802.0, "Credit": null, "Balance": 893709.62}, {"Serial No": "49", "Transaction Date": "02-07-2022", "Value Date": "02-07-2022", "Description": "UPI/202037219698/20:15:16/UPI/bharatpe.905000575", "Cheque Number": "", "Debit": 26.0, "Credit": null, "Balance": 893683.62}, {"Serial No": "50", "Transaction Date": "03-07-2022", "Value Date": "03-07-2022", "Description": "UPI/202141467912/08:55:21/UPI/gpay-", "Cheque Number": "", "Debit": 130.0, "Credit": null, "Balance": 893553.62}, {"Serial No": "51", "Transaction Date": "03-07-2022", "Value Date": "03-07-2022", "Description": "UPI/202153598715/20:10:08/UPI/bharatpe.905000575", "Cheque Number": "", "Debit": 29.0, "Credit": null, "Balance": 893524.62}, {"Serial No": "52", "Transaction Date": "04-07-2022", "Value Date": "04-07-2022", "Description": "UPI/202257849176/08:47:32/UPI/gpay-", "Cheque Number": "", "Debit": 130.0, "Credit": null, "Balance": 893394.62}, {"Serial No": "53", "Transaction Date": "04-07-2022", "Value Date": "04-07-2022", "Description": "UPI/202269639964/19:48:32/UPI/paytmqr2810050501 01", "Cheque Number": "", "Debit": 26.0, "Credit": null, "Balance": 893368.62}, {"Serial No": "54", "Transaction Date": "04-07-2022", "Value Date": "04-07-2022", "Description": "UPI/202378140779/13:28:27/UPI/paytmqr2810050501 01", "Cheque Number": "", "Debit": 30.0, "Credit": null, "Balance": 893338.62}, {"Serial No": "55", "Transaction Date": "04-07-2022", "Value Date": "04-07-2022", "Description": "UPI/202383682126/19:30:34/UPI/paytmqr2810050501 01", "Cheque Number": "", "Debit": 50.0, "Credit": null, "Balance": 893288.62}, {"Serial No": "56", "Transaction Date": "04-07-2022", "Value Date": "04-07-2022", "Description": "UPI/202488057454/08:53:14/UPI/gpay-", "Cheque Number": "", "Debit": 130.0, "Credit": null, "Balance": 893158.62}, {"Serial No": "57", "Transaction Date": "04-07-2022", "Value Date": "04-07-2022", "Description": "UPI/************/13:18:26/UPI/paytmqr2810050501 01", "Cheque Number": "", "Debit": 452.82, "Credit": null, "Balance": 892705.8}, {"Serial No": "58", "Transaction Date": "06-07-2022", "Value Date": "06-07-2022", "Description": "UPI/************/14:09:31/UPI/paytmqr2810050501 01", "Cheque Number": "", "Debit": 40.0, "Credit": null, "Balance": 892665.8}, {"Serial No": "59", "Transaction Date": "06-07-2022", "Value Date": "06-07-2022", "Description": "UPI/************/20:01:21/UPI/paybpcl@idfcbank/p", "Cheque Number": "", "Debit": 2500.0, "Credit": null, "Balance": 890165.8}, {"Serial No": "60", "Transaction Date": "06-07-2022", "Value Date": "06-07-2022", "Description": "UPI/************/20:05:05/UPI/paybpcl@idfcbank/R", "Cheque Number": "", "Debit": null, "Credit": 18.61, "Balance": 890184.41}, {"Serial No": "61", "Transaction Date": "07-07-2022", "Value Date": "07-07-2022", "Description": "UPI/************/08:46:18/UPI/gpay-", "Cheque Number": "", "Debit": 130.0, "Credit": null, "Balance": 890054.41}, {"Serial No": "62", "Transaction Date": "07-07-2022", "Value Date": "07-07-2022", "Description": "UPI/************/11:04:53/UPI/euronetgpay.pay@ici 01", "Cheque Number": "", "Debit": 510.0, "Credit": null, "Balance": 889544.41}, {"Serial No": "63", "Transaction Date": "09-07-2022", "Value Date": "09-07-2022", "Description": "UPI/************/18:05:42/UPI/paytmqr2810050501 01", "Cheque Number": "", "Debit": 20.0, "Credit": null, "Balance": 889524.41}, {"Serial No": "64", "Transaction Date": "11-07-2022", "Value Date": "11-07-2022", "Description": "UPI/************/20:14:28/UPI/paytmqr2810050501 01", "Cheque Number": "", "Debit": 26.0, "Credit": null, "Balance": 889498.41}, {"Serial No": "65", "Transaction Date": "11-07-2022", "Value Date": "11-07-2022", "Description": "UPI/202527112364/20:23:38/UPI/paytmqr2810050501 01", "Cheque Number": "", "Debit": 40.0, "Credit": null, "Balance": 889458.41}, {"Serial No": "66", "Transaction Date": "11-07-2022", "Value Date": "11-07-2022", "Description": "UPI/202631018118/08:49:46/UPI/gpay-", "Cheque Number": "", "Debit": 130.0, "Credit": null, "Balance": 889328.41}, {"Serial No": "67", "Transaction Date": "12-07-2022", "Value Date": "12-07-2022", "Description": "UPI/202636799630/14:58:07/UPI/q47097502@ybl/ok 01", "Cheque Number": "", "Debit": 100.0, "Credit": null, "Balance": 889228.41}, {"Serial No": "68", "Transaction Date": "13-07-2022", "Value Date": "13-07-2022", "Description": "UPI/202639040145/17:26:59/UPI/paytmqr2810050501 01", "Cheque Number": "", "Debit": 15.0, "Credit": null, "Balance": 889213.41}, {"Serial No": "69", "Transaction Date": "17-07-2022", "Value Date": "17-07-2022", "Description": "UPI/202640481170/18:41:15/UPI/gpay-", "Cheque Number": "", "Debit": 20.0, "Credit": null, "Balance": 889193.41}, {"Serial No": "70", "Transaction Date": "17-07-2022", "Value Date": "17-07-2022", "Description": "UPI/202642779722/20:28:09/UPI/paytmqr2810050501 01", "Cheque Number": "", "Debit": 26.0, "Credit": null, "Balance": 889167.41}, {"Serial No": "71", "Transaction Date": "17-07-2022", "Value Date": "17-07-2022", "Description": "UPI/202643090551/20:45:06/UPI/paytmqr2810050501 01", "Cheque Number": "", "Debit": 40.0, "Credit": null, "Balance": 889127.41}, {"Serial No": "72", "Transaction Date": "17-07-2022", "Value Date": "17-07-2022", "Description": "UPI/202746299654/08:55:33/UPI/gpay-", "Cheque Number": "", "Debit": 130.0, "Credit": null, "Balance": 888997.41}, {"Serial No": "73", "Transaction Date": "17-07-2022", "Value Date": "17-07-2022", "Description": "UPI/202754479902/17:21:44/UPI/q393159212@ybl/ok 01", "Cheque Number": "", "Debit": 260.0, "Credit": null, "Balance": 888737.41}, {"Serial No": "74", "Transaction Date": "17-07-2022", "Value Date": "17-07-2022", "Description": "UPI/202756952403/19:21:09/UPI/paytmqr2810050501 01", "Cheque Number": "", "Debit": 26.0, "Credit": null, "Balance": 888711.41}, {"Serial No": "77", "Transaction Date": "20-07-2022", "Value Date": "20-07-2022", "Description": "UPI/202872652555/19:11:22/UPI/paytmqr2810050501 01", "Cheque Number": "", "Debit": 1040.0, "Credit": null, "Balance": 885421.41}, {"Serial No": "78", "Transaction Date": "21-07-2022", "Value Date": "21-07-2022", "Description": "UPI/203207269349/15:38:35/UPI/kaushik243911@ybl/", "Cheque Number": "", "Debit": 1.0, "Credit": null, "Balance": 885420.41}, {"Serial No": "79", "Transaction Date": "22-07-2022", "Value Date": "22-07-2022", "Description": "UPI/203224655512/15:39:05/UPI/kaushik243911@ybl/", "Cheque Number": "", "Debit": 1499.0, "Credit": null, "Balance": 883921.41}, {"Serial No": "80", "Transaction Date": "22-07-2022", "Value Date": "22-07-2022", "Description": "BY CASH", "Cheque Number": "", "Debit": null, "Credit": 52000.0, "Balance": 935921.41}, {"Serial No": "81", "Transaction Date": "25-07-2022", "Value Date": "25-07-2022", "Description": "UPI/203639074416/14:26:15/UPI/billdesk.prepaid-mo @", "Cheque Number": "", "Debit": 99.0, "Credit": null, "Balance": 935822.41}, {"Serial No": "82", "Transaction Date": "25-07-2022", "Value Date": "25-07-2022", "Description": "UPI/203641212301/16:28:18/UPI/patelkalpesh151987 @", "Cheque Number": "", "Debit": 7000.0, "Credit": null, "Balance": 928822.41}, {"Serial No": "83", "Transaction Date": "25-07-2022", "Value Date": "25-07-2022", "Description": "UPI/203644037389/18:45:55/UPI/paytmqr2810050501 01", "Cheque Number": "", "Debit": 26.0, "Credit": null, "Balance": 928796.41}, {"Serial No": "84", "Transaction Date": "27-07-2022", "Value Date": "27-07-2022", "Description": "0U1PI/206096429666/20:23:42/UPI/paytmqr28100505 01", "Cheque Number": "", "Debit": null, "Credit": 62.0, "Balance": 928858.41}, {"Serial No": "85", "Transaction Date": "27-07-2022", "Value Date": "27-07-2022", "Description": "UPI/203876710043/17:17:21/UPI/paytmqr2810050501 01", "Cheque Number": "", "Debit": 40.0, "Credit": null, "Balance": 928818.41}, {"Serial No": "86", "Transaction Date": "27-07-2022", "Value Date": "27-07-2022", "Description": "UPI/203876797965/17:21:50/UPI/bharatpe.904222137", "Cheque Number": "", "Debit": 140.0, "Credit": null, "Balance": 928678.41}, {"Serial No": "87", "Transaction Date": "28-07-2022", "Value Date": "28-07-2022", "Description": "UPI/203876879149/17:26:00/UPI/bharatpe096000013 65", "Cheque Number": "", "Debit": 95.0, "Credit": null, "Balance": 928583.41}, {"Serial No": "88", "Transaction Date": "29-07-2022", "Value Date": "29-07-2022", "Description": "UPI/203882187353/21:07:48/UPI/bharatpe.905000575", "Cheque Number": "", "Debit": 29.0, "Credit": null, "Balance": 928554.41}, {"Serial No": "89", "Transaction Date": "30-07-2022", "Value Date": "30-07-2022", "Description": "UPI/203994968673/17:14:01/UPI/q40238104@ybl/jain 01", "Cheque Number": "", "Debit": 530.0, "Credit": null, "Balance": 928024.41}, {"Serial No": "90", "Transaction Date": "30-07-2022", "Value Date": "30-07-2022", "Description": "UPI/203995049365/17:18:03/UPI/paytmqr2810050501 01", "Cheque Number": "", "Debit": 90.0, "Credit": null, "Balance": 927934.41}, {"Serial No": "91", "Transaction Date": "02-08-2022", "Value Date": "02-08-2022", "Description": "UPI/203995130393/17:22:03/UPI/q393159212@ybl/ok 01", "Cheque Number": "", "Debit": 280.0, "Credit": null, "Balance": 927654.41}, {"Serial No": "92", "Transaction Date": "02-08-2022", "Value Date": "02-08-2022", "Description": "UPI/203995171431/17:24:06/UPI/paytmqr2810050501 01", "Cheque Number": "", "Debit": 380.0, "Credit": null, "Balance": 927274.41}, {"Serial No": "93", "Transaction Date": "03-08-2022", "Value Date": "03-08-2022", "Description": "UPI/203999938126/20:38:42/UPI/paytmqr2810050501 01", "Cheque Number": "", "Debit": 50.0, "Credit": null, "Balance": 927224.41}, {"Serial No": "94", "Transaction Date": "03-08-2022", "Value Date": "03-08-2022", "Description": "UPI/204054680607/11:09:16/UPI/kaushik243911@ybl/", "Cheque Number": "", "Debit": null, "Credit": 15000.0, "Balance": 942224.41}, {"Serial No": "95", "Transaction Date": "04-08-2022", "Value Date": "04-08-2022", "Description": "UPI/204035048707/19:40:28/UPI/q521350411@ybl/ok 01", "Cheque Number": "", "Debit": 40.0, "Credit": null, "Balance": 942184.41}, {"Serial No": "96", "Transaction Date": "05-08-2022", "Value Date": "05-08-2022", "Description": "UPI/204178149379/09:01:06/UPI/paytmqr2810050501 01", "Cheque Number": "", "Debit": 410.0, "Credit": null, "Balance": 941774.41}, {"Serial No": "97", "Transaction Date": "05-08-2022", "Value Date": "05-08-2022", "Description": "UPI/204140403693/09:01:13/UPI/paytmqr2810050501 01", "Cheque Number": "", "Debit": 410.0, "Credit": null, "Balance": 941364.41}, {"Serial No": "98", "Transaction Date": "05-08-2022", "Value Date": "05-08-2022", "Description": "UPI/204142364862/11:23:33/UPI/paytmqr2810050501 01", "Cheque Number": "", "Debit": 214.0, "Credit": null, "Balance": 941150.41}, {"Serial No": "99", "Transaction Date": "06-08-2022", "Value Date": "06-08-2022", "Description": "UPI/204184142208/11:53:06/UPI/paytmqr2810050501 01", "Cheque Number": "", "Debit": 450.0, "Credit": null, "Balance": 940700.41}, {"Serial No": "100", "Transaction Date": "06-08-2022", "Value Date": "06-08-2022", "Description": "UPI/204115664207/21:14:53/UPI/BHARATPE.905000", "Cheque Number": "", "Debit": 26.0, "Credit": null, "Balance": 940674.41}, {"Serial No": "101", "Transaction Date": "07-08-2022", "Value Date": "07-08-2022", "Description": "UPI/204137339493/21:19:40/UPI/paytmqr2810050501 01", "Cheque Number": "", "Debit": 40.0, "Credit": null, "Balance": 940634.41}, {"Serial No": "102", "Transaction Date": "08-08-2022", "Value Date": "08-08-2022", "Description": "ATM/CASH/204212008382/XXXXXXXXXXXX8140", "Cheque Number": "", "Debit": 2000.0, "Credit": null, "Balance": 938634.41}, {"Serial No": "103", "Transaction Date": "10-08-2022", "Value Date": "10-08-2022", "Description": "UPI/204273709638/14:36:19/UPI/q891670219@ybl/ok", "Cheque Number": "", "Debit": 30.0, "Credit": null, "Balance": 938604.41}, {"Serial No": "104", "Transaction Date": "11-08-2022", "Value Date": "11-08-2022", "Description": "UPI/204275143594/15:12:57/UPI/dhruvildixit2@oksbi 01", "Cheque Number": "", "Debit": 50.0, "Credit": null, "Balance": 938554.41}, {"Serial No": "105", "Transaction Date": "11-08-2022", "Value Date": "11-08-2022", "Description": "UPI/204285732170/20:28:41/UPI/paytmqr2810050501 01", "Cheque Number": "", "Debit": 26.0, "Credit": null, "Balance": 938528.41}, {"Serial No": "106", "Transaction Date": "13-08-2022", "Value Date": "13-08-2022", "Description": "UPI/204286122621/20:45:05/UPI/paytmqr2810050501 01", "Cheque Number": "", "Debit": 50.0, "Credit": null, "Balance": 938478.41}, {"Serial No": "107", "Transaction Date": "14-08-2022", "Value Date": "14-08-2022", "Description": "UPI/204424183572/14:00:15/UPI/9978522200@okbiz axi", "Cheque Number": "", "Debit": 680.0, "Credit": null, "Balance": 937798.41}, {"Serial No": "108", "Transaction Date": "16-08-2022", "Value Date": "16-08-2022", "Description": "UPI/204425479133/15:16:56/UPI/paytmqr2810050501 01", "Cheque Number": "", "Debit": 2500.0, "Credit": null, "Balance": 935298.41}, {"Serial No": "109", "Transaction Date": "16-08-2022", "Value Date": "16-08-2022", "Description": "UPI/204425572161/15:22:51/UPI/paytmqr2810050501 01", "Cheque Number": "", "Debit": 11.0, "Credit": null, "Balance": 935287.41}, {"Serial No": "110", "Transaction Date": "17-08-2022", "Value Date": "17-08-2022", "Description": "UPI/204425610795/15:25:19/UPI/paytmqr2810050501 01", "Cheque Number": "", "Debit": 110.0, "Credit": null, "Balance": 935177.41}, {"Serial No": "111", "Transaction Date": "17-08-2022", "Value Date": "17-08-2022", "Description": "UPI/204425780984/15:36:15/UPI/paytmqr2810050501 01", "Cheque Number": "", "Debit": 300.0, "Credit": null, "Balance": 934877.41}, {"Serial No": "112", "Transaction Date": "19-08-2022", "Value Date": "19-08-2022", "Description": "UPI/204543298682/15:15:02/UPI/patelkalpesh151987 @", "Cheque Number": "", "Debit": 200.0, "Credit": null, "Balance": 934677.41}, {"Serial No": "113", "Transaction Date": "19-08-2022", "Value Date": "19-08-2022", "Description": "UPI/204543311792/15:15:42/UPI/billdesk.electricit", "Cheque Number": "", "Debit": 40.0, "Credit": null, "Balance": 934637.41}, {"Serial No": "114", "Transaction Date": "20-08-2022", "Value Date": "20-08-2022", "Description": "UPI/204656545495/10:25:13/UPI/euronetgpay.pay@ici", "Cheque Number": "", "Debit": 179.0, "Credit": null, "Balance": 934458.41}, {"Serial No": "115", "Transaction Date": "21-08-2022", "Value Date": "21-08-2022", "Description": "UPI/204774926866/09:56:55/UPI/billdesk.prepaid-mo", "Cheque Number": "", "Debit": 99.0, "Credit": null, "Balance": 934359.41}, {"Serial No": "116", "Transaction Date": "22-08-2022", "Value Date": "22-08-2022", "Description": "BY CASH", "Cheque Number": "", "Debit": null, "Credit": 34000.0, "Balance": 968359.41}, {"Serial No": "117", "Transaction Date": "24-08-2022", "Value Date": "24-08-2022", "Description": "UPI/204780983392/15:13:59/UPI/paytmqr2810050501 01", "Cheque Number": "", "Debit": 230.0, "Credit": null, "Balance": 968129.41}, {"Serial No": "118", "Transaction Date": "25-08-2022", "Value Date": "25-08-2022", "Description": "ATM/CASH/5032/XXXXXXXXXXXX8140", "Cheque Number": "", "Debit": 2000.0, "Credit": null, "Balance": 966129.41}, {"Serial No": "119", "Transaction Date": "27-08-2022", "Value Date": "27-08-2022", "Description": "UPI/204784825975/18:32:04/UPI/maulikra<PERSON><PERSON>@ok ax", "Cheque Number": "", "Debit": 1114.0, "Credit": null, "Balance": 965015.41}, {"Serial No": "120", "Transaction Date": "27-08-2022", "Value Date": "27-08-2022", "Description": "UPI/204738556080/19:43:49/UPI/billdesk-tez@icici/", "Cheque Number": "", "Debit": null, "Credit": 40.0, "Balance": 965055.41}, {"Serial No": "121", "Transaction Date": "28-08-2022", "Value Date": "28-08-2022", "Description": "UPI/204893196193/09:49:21/UPI/paytmqr2810050501 01", "Cheque Number": "", "Debit": 225.0, "Credit": null, "Balance": 964830.41}, {"Serial No": "122", "Transaction Date": "29-08-2022", "Value Date": "29-08-2022", "Description": "UPI/204897648317/13:44:59/UPI/solanki1598@okaxis /", "Cheque Number": "", "Debit": 100.0, "Credit": null, "Balance": 964730.41}, {"Serial No": "123", "Transaction Date": "31-08-2022", "Value Date": "31-08-2022", "Description": "UPI/204808293950/13:46:30/UPI/solanki1598@okicici 01", "Cheque Number": "", "Debit": null, "Credit": 1780.0, "Balance": 966510.41}, {"Serial No": "124", "Transaction Date": "31-08-2022", "Value Date": "31-08-2022", "Description": "UPI/204924229710/11:07:03/UPI/paytmqr2810050501 01", "Cheque Number": "", "Debit": 160.0, "Credit": null, "Balance": 966350.41}, {"Serial No": "125", "Transaction Date": "01-09-2022", "Value Date": "01-09-2022", "Description": "UPI/205178878246/19:46:34/UPI/gpay-", "Cheque Number": "", "Debit": 262.0, "Credit": null, "Balance": 966088.41}, {"Serial No": "126", "Transaction Date": "02-09-2022", "Value Date": "02-09-2022", "Description": "UPI/205178901496/19:47:30/UPI/gpay-", "Cheque Number": "", "Debit": 50.0, "Credit": null, "Balance": 966038.41}, {"Serial No": "127", "Transaction Date": "03-09-2022", "Value Date": "03-09-2022", "Description": "UPI/205178990923/19:50:53/UPI/paytmqr2810050501 01", "Cheque Number": "", "Debit": 26.0, "Credit": null, "Balance": 966012.41}, {"Serial No": "128", "Transaction Date": "03-09-2022", "Value Date": "03-09-2022", "Description": "UPI/205286409265/10:33:01/UPI/q86648939@ybl/dies", "Cheque Number": "", "Debit": 3564.8, "Credit": null, "Balance": 962447.61}, {"Serial No": "129", "Transaction Date": "05-09-2022", "Value Date": "05-09-2022", "Description": "ATM/CASH/6330/XXXXXXXXXXXX8140", "Cheque Number": "", "Debit": 3000.0, "Credit": null, "Balance": 959447.61}, {"Serial No": "130", "Transaction Date": "06-09-2022", "Value Date": "06-09-2022", "Description": "UPI/205321734838/09:40:50/UPI/paytmqr2810050501 01", "Cheque Number": "", "Debit": 799.71, "Credit": null, "Balance": 958647.9}, {"Serial No": "131", "Transaction Date": "07-09-2022", "Value Date": "07-09-2022", "Description": "UPI/205321734838/09:40:50/UPI/paytmqr2810050501 01", "Cheque Number": "", "Debit": 155.0, "Credit": null, "Balance": 958492.9}, {"Serial No": "132", "Transaction Date": "08-09-2022", "Value Date": "08-09-2022", "Description": "UPI/************/20:42:04/UPI/paytmqr2810050501 01", "Cheque Number": "", "Debit": 26.0, "Credit": null, "Balance": 958466.9}, {"Serial No": "133", "Transaction Date": "09-09-2022", "Value Date": "09-09-2022", "Description": "UPI/************/00:46:47/UPI/goog-", "Cheque Number": "", "Debit": null, "Credit": 6.0, "Balance": 958472.9}, {"Serial No": "134", "Transaction Date": "10-09-2022", "Value Date": "10-09-2022", "Description": "UPI/************/13:29:51/UPI/xubiitech@yesbank/I 01", "Cheque Number": "", "Debit": 50.0, "Credit": null, "Balance": 958422.9}, {"Serial No": "135", "Transaction Date": "13-09-2022", "Value Date": "13-09-2022", "Description": "UPI/************/20:51:14/UPI/paytmqr2810050501 01", "Cheque Number": "", "Debit": 29.0, "Credit": null, "Balance": 958393.9}, {"Serial No": "136", "Transaction Date": "13-09-2022", "Value Date": "13-09-2022", "Description": "UPI/************/22:00:23/UPI/xubiitech@yesbank/I", "Cheque Number": "", "Debit": 50.0, "Credit": null, "Balance": 958343.9}, {"Serial No": "137", "Transaction Date": "13-09-2022", "Value Date": "13-09-2022", "Description": "UPI/************/22:11:57/UPI/xubiitech@yesbank/I", "Cheque Number": "", "Debit": 500.0, "Credit": null, "Balance": 957843.9}, {"Serial No": "138", "Transaction Date": "14-09-2022", "Value Date": "14-09-2022", "Description": "UPI/************/10:56:43/UPI/ippo@yesbank/undef", "Cheque Number": "", "Debit": null, "Credit": 25500.0, "Balance": 983343.9}, {"Serial No": "139", "Transaction Date": "21-09-2022", "Value Date": "21-09-2022", "Description": "PRCR/Ketav Auto Service/AHMEDABAD", "Cheque Number": "", "Debit": 500.0, "Credit": null, "Balance": 982843.9}, {"Serial No": "140", "Transaction Date": "22-09-2022", "Value Date": "22-09-2022", "Description": "UPI/************/11:12:14/UPI/xubiitech@yesbank/I", "Cheque Number": "", "Debit": 500.0, "Credit": null, "Balance": 982343.9}, {"Serial No": "141", "Transaction Date": "24-09-2022", "Value Date": "24-09-2022", "Description": "UPI/************/12:38:31/UPI/paytm-", "Cheque Number": "", "Debit": 350.0, "Credit": null, "Balance": 981993.9}, {"Serial No": "142", "Transaction Date": "24-09-2022", "Value Date": "24-09-2022", "Description": "UPI/************/21:11:11/UPI/paytmqr2810050501 01", "Cheque Number": "", "Debit": 40.0, "Credit": null, "Balance": 981953.9}, {"Serial No": "143", "Transaction Date": "24-09-2022", "Value Date": "24-09-2022", "Description": "UPI/************/21:20:31/UPI/paytmqr2810050501 01", "Cheque Number": "", "Debit": 29.0, "Credit": null, "Balance": 981924.9}, {"Serial No": "144", "Transaction Date": "24-09-2022", "Value Date": "24-09-2022", "Description": "UPI/************/22:05:48/UPI/xubiitech@yesbank/I", "Cheque Number": "", "Debit": 500.0, "Credit": null, "Balance": 981424.9}, {"Serial No": "145", "Transaction Date": "24-09-2022", "Value Date": "24-09-2022", "Description": "UPI/************/22:19:08/UPI/xubiitech@yesbank/I", "Cheque Number": "", "Debit": 500.0, "Credit": null, "Balance": 980924.9}, {"Serial No": "146", "Transaction Date": "25-09-2022", "Value Date": "25-09-2022", "Description": "UPI/************/13:29:51/UPI/q12325437@ybl/ok", "Cheque Number": "", "Debit": 380.0, "Credit": null, "Balance": 980544.9}, {"Serial No": "147", "Transaction Date": "25-09-2022 25-09-2022", "Value Date": "", "Description": "UPI/************/20:18:50/UPI/paytmqr2810050501 01", "Cheque Number": "", "Debit": 26.0, "Credit": null, "Balance": 980518.9}, {"Serial No": "148", "Transaction Date": "25-09-2022 25-09-2022", "Value Date": "", "Description": "UPI/************/21:02:06/UPI/ashenfallousipp@ye", "Cheque Number": "", "Debit": 200.0, "Credit": null, "Balance": 980318.9}, {"Serial No": "149", "Transaction Date": "25-09-2022 25-09-2022", "Value Date": "", "Description": "UPI/************/21:39:36/UPI/ashenfallousipp@ye", "Cheque Number": "", "Debit": 500.0, "Credit": null, "Balance": 979818.9}, {"Serial No": "150", "Transaction Date": "26-09-2022 26-09-2022", "Value Date": "", "Description": "UPI/************/23:57:12/UPI/ippo@yesbank/undef", "Cheque Number": "", "Debit": null, "Credit": 12000.0, "Balance": 991818.9}, {"Serial No": "151", "Transaction Date": "26-09-2022 26-09-2022", "Value Date": "", "Description": "ATM/CASH/6443/XXXXXXXXXXXX8140", "Cheque Number": "", "Debit": 2000.0, "Credit": null, "Balance": 989818.9}, {"Serial No": "152", "Transaction Date": "26-09-2022 26-09-2022", "Value Date": "", "Description": "UPI/************/19:51:40/UPI/paytmqr2810050501 01", "Cheque Number": "", "Debit": 26.0, "Credit": null, "Balance": 989792.9}, {"Serial No": "153", "Transaction Date": "26-09-2022 26-09-2022", "Value Date": "", "Description": "UPI/************/20:28:55/UPI/xubiitech@yesbank/I", "Cheque Number": "", "Debit": 200.0, "Credit": null, "Balance": 989592.9}, {"Serial No": "154", "Transaction Date": "26-09-2022 26-09-2022", "Value Date": "", "Description": "UPI/************/21:20:27/UPI/xubiitech@yesbank/I", "Cheque Number": "", "Debit": 500.0, "Credit": null, "Balance": 989092.9}, {"Serial No": "155", "Transaction Date": "28-09-2022 28-09-2022", "Value Date": "", "Description": "UPI/************/15:29:00/UPI/xubiitech@yesbank/I", "Cheque Number": "", "Debit": 1000.0, "Credit": null, "Balance": 988092.9}, {"Serial No": "156", "Transaction Date": "28-09-2022 28-09-2022", "Value Date": "", "Description": "UPI/************/16:26:22/UPI/xubiitech@yesbank/I @", "Cheque Number": "", "Debit": 1000.0, "Credit": null, "Balance": 987092.9}, {"Serial No": "157", "Transaction Date": "28-09-2022 28-09-2022", "Value Date": "", "Description": "UPI/************/18:26:15/UPI/patelkalpesh151987 @", "Cheque Number": "", "Debit": null, "Credit": 30000.0, "Balance": 1017092.9}, {"Serial No": "158", "Transaction Date": "28-09-2022 28-09-2022", "Value Date": "", "Description": "UPI/************/18:40:17/UPI/xubiitech@yesbank/I", "Cheque Number": "", "Debit": 200.0, "Credit": null, "Balance": 1016892.9}, {"Serial No": "159", "Transaction Date": "28-09-2022 28-09-2022", "Value Date": "", "Description": "UPI/************/18:46:12/UPI/euronetgpay.pay@ici", "Cheque Number": "", "Debit": 510.0, "Credit": null, "Balance": 1016382.9}, {"Serial No": "160", "Transaction Date": "28-09-2022 28-09-2022", "Value Date": "", "Description": "UPI/************/18:49:42/UPI/xubiitech@yesbank/I", "Cheque Number": "", "Debit": 500.0, "Credit": null, "Balance": 1015882.9}, {"Serial No": "161", "Transaction Date": "28-09-2022 28-09-2022", "Value Date": "", "Description": "UPI/************/18:49:49/REVERSAL", "Cheque Number": "", "Debit": null, "Credit": 50000.0, "Balance": 1065882.9}, {"Serial No": "162", "Transaction Date": "28-09-2022 28-09-2022", "Value Date": "", "Description": "UPI/************/19:08:07/UPI/xubiitech@yesbank/I", "Cheque Number": "", "Debit": 500.0, "Credit": null, "Balance": 1065382.9}, {"Serial No": "163", "Transaction Date": "28-09-2022 28-09-2022", "Value Date": "", "Description": "UPI/************/15:52:33/UPI/goog-", "Cheque Number": "", "Debit": null, "Credit": 7.0, "Balance": 1065389.9}, {"Serial No": "164", "Transaction Date": "28-09-2022 28-09-2022", "Value Date": "", "Description": "MBK/************/09:02:53/ABMALAL", "Cheque Number": "", "Debit": null, "Credit": 45000.0, "Balance": 1110389.9}, {"Serial No": "167", "Transaction Date": "29-09-2022 28-09-2022", "Value Date": "", "Description": "UPI/************/12:07:42/UPI/q47097502@ybl/ok 01", "Cheque Number": "", "Debit": 105.0, "Credit": null, "Balance": 1108034.9}, {"Serial No": "168", "Transaction Date": "30-09-2022 28-09-2022", "Value Date": "", "Description": "UPI/************/19:57:33/UPI/paytmqr2810050501 01", "Cheque Number": "", "Debit": 26.0, "Credit": null, "Balance": 1108008.9}, {"Serial No": "169", "Transaction Date": "01-10-2022 01-10-2022", "Value Date": "", "Description": "27740100010512:Int.Pd:01-07-2022 to 30-09-2022", "Cheque Number": "", "Debit": null, "Credit": 6337.0, "Balance": 1114345.9}, {"Serial No": "170", "Transaction Date": "02-10-2022 02-10-2022", "Value Date": "", "Description": "MBK/206105880625/09:56:56/ABMALAL", "Cheque Number": "", "Debit": null, "Credit": null, "Balance": null}, {"Serial No": "171", "Transaction Date": "02-10-2022 02-10-2022", "Value Date": "", "Description": "UPI/206122938918/17:53:08/UPI/paytmqr2810050501 01", "Cheque Number": "", "Debit": null, "Credit": null, "Balance": null}, {"Serial No": "172", "Transaction Date": "02-10-2022 02-10-2022", "Value Date": "", "Description": "UPI/206125772497/19:39:46/UPI/paytmqr2810050501 01", "Cheque Number": "", "Debit": null, "Credit": null, "Balance": null}, {"Serial No": "173", "Transaction Date": "03-10-2022", "Value Date": "03-10-2022", "Description": "UPI/206232728731/09:10:15/UPI/paytmqr2810050501 01", "Cheque Number": "", "Debit": 811.0, "Credit": null, "Balance": 1142088.9}, {"Serial No": "174", "Transaction Date": "04-10-2022", "Value Date": "04-10-2022", "Description": "SELF", "Cheque Number": "26", "Debit": 80000.0, "Credit": null, "Balance": 1062088.9}, {"Serial No": "175", "Transaction Date": "04-10-2022", "Value Date": "04-10-2022", "Description": "UPI/************/15:52:33/UPI/goog-", "Cheque Number": "", "Debit": null, "Credit": 6.0, "Balance": 1062094.9}, {"Serial No": "176", "Transaction Date": "05-10-2022", "Value Date": "05-10-2022", "Description": "UPI/206247010686/19:44:53/UPI/paytmqr2810050501 01", "Cheque Number": "", "Debit": 27.0, "Credit": null, "Balance": 1062067.9}, {"Serial No": "177", "Transaction Date": "05-10-2022", "Value Date": "05-10-2022", "Description": "UPI/206357556873/11:45:23/UPI/solanki1598@okaxis /", "Cheque Number": "", "Debit": 500.0, "Credit": null, "Balance": 1061567.9}, {"Serial No": "178", "Transaction Date": "06-10-2022", "Value Date": "06-10-2022", "Description": "UPI/206357571327/11:45:54/UPI/goog-", "Cheque Number": "", "Debit": null, "Credit": 6.0, "Balance": 1061573.9}, {"Serial No": "179", "Transaction Date": "06-10-2022", "Value Date": "06-10-2022", "Description": "UPI/206372969233/21:14:26/UPI/paytmqr2810050501 01", "Cheque Number": "", "Debit": 27.0, "Credit": null, "Balance": 1061546.9}, {"Serial No": "180", "Transaction Date": "06-10-2022", "Value Date": "06-10-2022", "Description": "TO TRANSFER", "Cheque Number": "", "Debit": null, "Credit": 32000.0, "Balance": 1093546.9}, {"Serial No": "181", "Transaction Date": "08-10-2022", "Value Date": "08-10-2022", "Description": "UPI/206488122537/18:01:52/UPI/patelkalpesh151987 @", "Cheque Number": "", "Debit": 10000.0, "Credit": null, "Balance": 1083546.9}, {"Serial No": "182", "Transaction Date": "09-10-2022", "Value Date": "09-10-2022", "Description": "UPI/206488682557/18:24:35/UPI/**********@kkbk0 002", "Cheque Number": "", "Debit": 5000.0, "Credit": null, "Balance": 1078546.9}, {"Serial No": "183", "Transaction Date": "10-10-2022", "Value Date": "10-10-2022", "Description": "UPI/************/21:22:59/UPI/paytmqr2810050501 01", "Cheque Number": "", "Debit": 50.0, "Credit": null, "Balance": 1078496.9}, {"Serial No": "184", "Transaction Date": "11-10-2022", "Value Date": "11-10-2022", "Description": "UPI/************/21:28:01/UPI/paytmqr2810050501 01", "Cheque Number": "", "Debit": 27.0, "Credit": null, "Balance": 1078469.9}, {"Serial No": "185", "Transaction Date": "12-10-2022", "Value Date": "12-10-2022", "Description": "UPI/************/22:30:21/UPI/xubiitech@yesbank/I 01", "Cheque Number": "", "Debit": 500.0, "Credit": null, "Balance": 1077969.9}, {"Serial No": "186", "Transaction Date": "13-10-2022", "Value Date": "13-10-2022", "Description": "UPI/************/14:11:39/UPI/paytmqr2810050501 01", "Cheque Number": "", "Debit": 30.0, "Credit": null, "Balance": 1077939.9}, {"Serial No": "187", "Transaction Date": "14-10-2022", "Value Date": "14-10-2022", "Description": "UPI/************/15:53:12/UPI/xubiitech@yesbank/I", "Cheque Number": "", "Debit": 1000.0, "Credit": null, "Balance": 1076939.9}, {"Serial No": "188", "Transaction Date": "15-10-2022", "Value Date": "15-10-2022", "Description": "UPI/************/18:26:32/UPI/ippo@yesbank/undef", "Cheque Number": "", "Debit": null, "Credit": 27000.0, "Balance": 1103939.9}, {"Serial No": "189", "Transaction Date": "17-10-2022", "Value Date": "17-10-2022", "Description": "UPI/************/19:06:52/UPI/paytmqr2810050501 01", "Cheque Number": "", "Debit": 227.0, "Credit": null, "Balance": 1103712.9}, {"Serial No": "190", "Transaction Date": "17-10-2022", "Value Date": "17-10-2022", "Description": "UPI/************/19:08:54/UPI/paytmqr2810050501 01", "Cheque Number": "", "Debit": 37.0, "Credit": null, "Balance": 1103675.9}, {"Serial No": "191", "Transaction Date": "17-10-2022", "Value Date": "17-10-2022", "Description": "UPI/************/19:16:09/UPI/paytmqr2810050501 01", "Cheque Number": "", "Debit": 33.0, "Credit": null, "Balance": 1103642.9}, {"Serial No": "192", "Transaction Date": "21-10-2022", "Value Date": "21-10-2022", "Description": "UPI/************/20:42:11/UPI/xubiitech@yesbank/I", "Cheque Number": "", "Debit": 1000.0, "Credit": null, "Balance": 1102642.9}, {"Serial No": "193", "Transaction Date": "21-10-2022", "Value Date": "21-10-2022", "Description": "UPI/************/23:04:04/UPI/ippo@yesbank/undef", "Cheque Number": "", "Debit": null, "Credit": 31000.0, "Balance": 1133642.9}, {"Serial No": "194", "Transaction Date": "23-10-2022", "Value Date": "23-10-2022", "Description": "UPI/************/11:48:38/UPI/**********@kkbk0 002", "Cheque Number": "", "Debit": 1000.0, "Credit": null, "Balance": 1132642.9}, {"Serial No": "195", "Transaction Date": "25-10-2022", "Value Date": "25-10-2022", "Description": "UPI/************/21:13:17/UPI/paytmqr2810050501 01", "Cheque Number": "", "Debit": 50.0, "Credit": null, "Balance": 1132592.9}, {"Serial No": "196", "Transaction Date": "27-10-2022", "Value Date": "27-10-2022", "Description": "UPI/************/21:19:38/UPI/paytmqr2810050501 01", "Cheque Number": "", "Debit": 27.0, "Credit": null, "Balance": 1132565.9}, {"Serial No": "197", "Transaction Date": "28-10-2022", "Value Date": "28-10-2022", "Description": "UPI/************/09:05:23/UPI/q393159212@ybl/ok", "Cheque Number": "", "Debit": 200.0, "Credit": null, "Balance": 1132365.9}, {"Serial No": "198", "Transaction Date": "28-10-2022", "Value Date": "28-10-2022", "Description": "UPI/206748715871/11:20:11/UPI/billdesk.prepaid-mo", "Cheque Number": "", "Debit": 99.0, "Credit": null, "Balance": 1132266.9}, {"Serial No": "199", "Transaction Date": "31-10-2022", "Value Date": "31-10-2022", "Description": "UPI/206748728072/11:20:55/UPI/goog-", "Cheque Number": "", "Debit": null, "Credit": 6.0, "Balance": 1132272.9}, {"Serial No": "200", "Transaction Date": "01-11-2022", "Value Date": "01-11-2022", "Description": "UPI/206753905758/15:38:26/UPI/paytmqr2810050501 01", "Cheque Number": "", "Debit": 2500.0, "Credit": null, "Balance": 1129772.9}, {"Serial No": "201", "Transaction Date": "02-11-2022", "Value Date": "02-11-2022", "Description": "UPI/206756103028/17:34:00/UPI/paytmqr2810050501 01", "Cheque Number": "", "Debit": 70.0, "Credit": null, "Balance": 1129702.9}, {"Serial No": "202", "Transaction Date": "02-11-2022", "Value Date": "02-11-2022", "Description": "UPI/206761028970/20:30:32/UPI/paytmqr2810050501 01", "Cheque Number": "", "Debit": 27.0, "Credit": null, "Balance": 1129675.9}, {"Serial No": "203", "Transaction Date": "02-11-2022", "Value Date": "02-11-2022", "Description": "UPI/206869254492/11:30:21/UPI/zeeldoshi2011@okh df", "Cheque Number": "", "Debit": 550.0, "Credit": null, "Balance": 1129125.9}, {"Serial No": "204", "Transaction Date": "02-11-2022", "Value Date": "02-11-2022", "Description": "UPI/206871002618/12:51:28/UPI/goog-", "Cheque Number": "", "Debit": null, "Credit": 6.0, "Balance": 1129131.9}, {"Serial No": "205", "Transaction Date": "03-11-2022", "Value Date": "03-11-2022", "Description": "UPI/206879411539/19:17:16/UPI/paytmqr2810050501 01", "Cheque Number": "", "Debit": 27.0, "Credit": null, "Balance": 1129104.9}, {"Serial No": "206", "Transaction Date": "04-11-2022", "Value Date": "04-11-2022", "Description": "UPI/206998835459/17:58:19/UPI/billdesk.videocon-d 01", "Cheque Number": "", "Debit": 300.0, "Credit": null, "Balance": 1128804.9}, {"Serial No": "207", "Transaction Date": "10-11-2022", "Value Date": "10-11-2022", "Description": "UPI/206914651464/21:32:19/UPI/paytmqr2810050501 01", "Cheque Number": "", "Debit": 30.0, "Credit": null, "Balance": 1128774.9}, {"Serial No": "208", "Transaction Date": "11-11-2022", "Value Date": "11-11-2022", "Description": "SMS Alert charges for Qtr Mar-22", "Cheque Number": "", "Debit": 17.7, "Credit": null, "Balance": 1128757.2}, {"Serial No": "209", "Transaction Date": "12-11-2022", "Value Date": "12-11-2022", "Description": "BY CASH", "Cheque Number": "", "Debit": null, "Credit": 44000.0, "Balance": 1172757.2}, {"Serial No": "210", "Transaction Date": "12-11-2022", "Value Date": "12-11-2022", "Description": "UPI/207033198882/19:22:39/UPI/paytmqr2810050501 01", "Cheque Number": "", "Debit": 27.0, "Credit": null, "Balance": 1172730.2}, {"Serial No": "211", "Transaction Date": "13-11-2022", "Value Date": "13-11-2022", "Description": "UPI/************/08:28:54/UPI/paytmqr2810050501 01", "Cheque Number": "", "Debit": 75.0, "Credit": null, "Balance": 1172655.2}, {"Serial No": "212", "Transaction Date": "14-11-2022", "Value Date": "14-11-2022", "Description": "UPI/************/19:50:08/UPI/paytmqr2810050501 01", "Cheque Number": "", "Debit": 27.0, "Credit": null, "Balance": 1172628.2}, {"Serial No": "213", "Transaction Date": "14-11-2022", "Value Date": "14-11-2022", "Description": "UPI/************/20:23:10/UPI/xubiitech@yesbank/I", "Cheque Number": "", "Debit": 1000.0, "Credit": null, "Balance": 1171628.2}, {"Serial No": "214", "Transaction Date": "15-11-2022", "Value Date": "15-11-2022", "Description": "UPI/************/23:51:45/UPI/ippo@yesbank/undef", "Cheque Number": "", "Debit": null, "Credit": 12000.0, "Balance": 1183628.2}, {"Serial No": "215", "Transaction Date": "16-11-2022", "Value Date": "16-11-2022", "Description": "UPI/************/09:17:29/UPI/paytmqr2810050501 01", "Cheque Number": "", "Debit": 300.0, "Credit": null, "Balance": 1183328.2}, {"Serial No": "216", "Transaction Date": "17-11-2022", "Value Date": "17-11-2022", "Description": "UPI/************/11:19:57/UPI/xubiitech@yesbank/I", "Cheque Number": "", "Debit": 1000.0, "Credit": null, "Balance": 1182328.2}, {"Serial No": "217", "Transaction Date": "18-11-2022", "Value Date": "18-11-2022", "Description": "UPI/************/12:52:30/UPI/xubiitech@yesbank/I", "Cheque Number": "", "Debit": 1000.0, "Credit": null, "Balance": 1181328.2}, {"Serial No": "218", "Transaction Date": "19-11-2022", "Value Date": "19-11-2022", "Description": "UPI/************/23:50:01/UPI/ippo@yesbank/undef", "Cheque Number": "", "Debit": null, "Credit": 61000.0, "Balance": 1242328.2}, {"Serial No": "219", "Transaction Date": "19-11-2022", "Value Date": "19-11-2022", "Description": "UPI/************/21:03:43/UPI/paytmqr2810050501 01", "Cheque Number": "", "Debit": 27.0, "Credit": null, "Balance": 1242301.2}, {"Serial No": "220", "Transaction Date": "19-11-2022", "Value Date": "19-11-2022", "Description": "UPI/************/12:57:32/UPI/billdesk.prepaid-mo 01", "Cheque Number": "", "Debit": 99.0, "Credit": null, "Balance": 1242202.2}, {"Serial No": "221", "Transaction Date": "20-11-2022", "Value Date": "20-11-2022", "Description": "UPI/************/19:47:52/UPI/paytmqr2810050501 01", "Cheque Number": "", "Debit": 27.0, "Credit": null, "Balance": 1242175.2}, {"Serial No": "222", "Transaction Date": "21-11-2022", "Value Date": "21-11-2022", "Description": "UPI/************/19:59:09/UPI/paytmqr2810050501 01", "Cheque Number": "", "Debit": 27.0, "Credit": null, "Balance": 1242148.2}, {"Serial No": "223", "Transaction Date": "22-11-2022", "Value Date": "22-11-2022", "Description": "UPI/207663003735/09:54:42/UPI/billdesk.prepaid-mo 01", "Cheque Number": "", "Debit": 20.0, "Credit": null, "Balance": 1242128.2}, {"Serial No": "224", "Transaction Date": "22-11-2022", "Value Date": "22-11-2022", "Description": "UPI/207665014101/11:02:20/UPI/paytmqr2810050501 01", "Cheque Number": "", "Debit": 2673.6, "Credit": null, "Balance": 1239454.6}, {"Serial No": "251", "Transaction Date": "08-12-2022", "Value Date": "08-12-2022", "Description": "UPI/208455937021/19:52:13/UPI/paytmqr2810050501 01", "Cheque Number": "", "Debit": 30.0, "Credit": null, "Balance": 1289427.6}, {"Serial No": "252", "Transaction Date": "08-12-2022", "Value Date": "08-12-2022", "Description": "UPI/208573419653/18:52:13/UPI/paytmqr2810050501 01", "Cheque Number": "", "Debit": 27.0, "Credit": null, "Balance": 1289400.6}, {"Serial No": "253", "Transaction Date": "09-12-2022", "Value Date": "09-12-2022", "Description": "UPI/208688038366/14:09:35/UPI/billdesk.prepaid-mo", "Cheque Number": "", "Debit": 719.0, "Credit": null, "Balance": 1288681.6}, {"Serial No": "254", "Transaction Date": "10-12-2022", "Value Date": "10-12-2022", "Description": "UPI/208616779815/14:52:39/UPI/razorpayx.5595@icic 01", "Cheque Number": "", "Debit": null, "Credit": 51.0, "Balance": 1288732.6}, {"Serial No": "255", "Transaction Date": "11-12-2022", "Value Date": "11-12-2022", "Description": "UPI/208693930753/19:22:41/UPI/paytmqr2810050501 01", "Cheque Number": "", "Debit": 77.0, "Credit": null, "Balance": 1288655.6}]