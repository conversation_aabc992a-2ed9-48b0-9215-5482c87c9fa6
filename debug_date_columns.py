#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试日期列合并问题
"""

import pandas as pd
import tabula

def debug_date_columns():
    """调试日期列合并问题"""
    pdf_path = "../files/8-bob-623136717-BOB-STATEMENT.pdf"
    
    print('=' * 80)
    print('调试Serial 147-172的日期列合并问题')
    print('=' * 80)
    
    try:
        # 检查第7页（Serial 147-172应该在这里）
        dfs = tabula.read_pdf(pdf_path, pages='7', 
                            stream=True,
                            pandas_options={'header': None, 'dtype': str})
        
        if dfs:
            df = dfs[0]
            print(f"第7页原始表格形状: {df.shape}")
            print(f"列数: {len(df.columns)}")
            
            # 找到Serial 147的位置
            serial_147_pos = None
            for i, row in df.iterrows():
                serial_no = str(row.iloc[0]).strip() if len(row) > 0 else ""
                if serial_no == '147':
                    serial_147_pos = i
                    break
            
            if serial_147_pos is not None:
                print(f"\n📍 找到Serial 147在行{serial_147_pos}")
                
                # 检查Serial 147前后几行的原始数据结构
                print(f"\nSerial 147前后行的原始数据结构:")
                start_range = max(0, serial_147_pos - 2)
                end_range = min(len(df), serial_147_pos + 10)
                
                for i in range(start_range, end_range):
                    if i < len(df):
                        row = df.iloc[i]
                        print(f"\n行{i}:")
                        for col_idx in range(len(row)):
                            value = str(row.iloc[col_idx]).strip()
                            print(f"  列{col_idx}: '{value}'")
                
                # 检查列标题
                print(f"\n🔍 检查表头结构:")
                header_row = df.iloc[0]
                print(f"表头行:")
                for col_idx in range(len(header_row)):
                    value = str(header_row.iloc[col_idx]).strip()
                    print(f"  列{col_idx}: '{value}'")
                
                # 分析问题
                print(f"\n🤔 问题分析:")
                print(f"正常情况下应该有8列:")
                print(f"  列0: Serial No")
                print(f"  列1: Transaction Date") 
                print(f"  列2: Value Date")
                print(f"  列3: Description")
                print(f"  列4: Cheque Number")
                print(f"  列5: Debit")
                print(f"  列6: Credit") 
                print(f"  列7: Balance")
                
                print(f"\n当前实际列数: {len(df.columns)}")
                if len(df.columns) < 8:
                    print(f"❌ 列数不足，可能是tabula解析时列合并了")
                else:
                    print(f"✅ 列数正确")
    
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_date_columns()
