#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
银行PDF文档自动识别系统
基于PDF内容特征识别银行类型
"""

import os
import re
from typing import Dict, List, Tuple, Optional
from pathlib import Path

# 尝试导入可选依赖
try:
    import tabula
    TABULA_AVAILABLE = True
except ImportError:
    TABULA_AVAILABLE = False
    print("⚠️ tabula-py未安装，将跳过表格提取功能")

try:
    import PyPDF2
    PYPDF2_AVAILABLE = True
except ImportError:
    PYPDF2_AVAILABLE = False
    print("⚠️ PyPDF2未安装，将跳过PDF文本提取功能")

try:
    import pandas as pd
    PANDAS_AVAILABLE = True
except ImportError:
    PANDAS_AVAILABLE = False
    print("⚠️ pandas未安装，将跳过数据处理功能")

class BankPDFIdentifier:
    """
    银行PDF识别器
    基于内容特征自动识别银行类型
    """
    
    def __init__(self):
        # 银行识别规则库
        self.bank_patterns = {
            'SBI': {
                'name': 'State Bank of India (SBI)',
                'keywords': ['state bank of india', 'sbi', 'भारतीय स्टेट बैंक'],
                'patterns': [
                    r'state\s+bank\s+of\s+india',
                    r'\bsbi\b',
                    r'account\s+statement.*sbi',
                    r'www\.sbi\.co\.in'
                ],
                'table_headers': ['transaction date', 'value date', 'description', 'ref no', 'debit', 'credit', 'balance']
            },
            
            'HDFC': {
                'name': 'HDFC Bank',
                'keywords': ['hdfc bank', 'hdfc', 'housing development finance'],
                'patterns': [
                    r'hdfc\s+bank',
                    r'housing\s+development\s+finance',
                    r'www\.hdfcbank\.com',
                    r'hdfc.*statement'
                ],
                'table_headers': ['date', 'narration', 'chq/ref no', 'value dt', 'withdrawal amt', 'deposit amt', 'closing balance']
            },
            
            'ICICI': {
                'name': 'ICICI Bank',
                'keywords': ['icici bank', 'icici', 'industrial credit'],
                'patterns': [
                    r'icici\s+bank',
                    r'industrial\s+credit\s+and\s+investment',
                    r'www\.icicibank\.com',
                    r'icici.*statement'
                ],
                'table_headers': ['date', 'particulars', 'cheque no', 'debit', 'credit', 'balance']
            },
            
            'BOB': {
                'name': 'Bank of Baroda (BOB)',
                'keywords': ['bank of baroda', 'bob', 'baroda'],
                'patterns': [
                    r'bank\s+of\s+baroda',
                    r'\bbob\b',
                    r'www\.bankofbaroda\.in',
                    r'baroda.*statement'
                ],
                'table_headers': ['serial no', 'transaction date', 'value date', 'description', 'cheque number', 'debit', 'credit', 'balance']
            },
            
            'KOTAK': {
                'name': 'Kotak Mahindra Bank',
                'keywords': ['kotak mahindra', 'kotak bank', 'kotak'],
                'patterns': [
                    r'kotak\s+mahindra\s+bank',
                    r'kotak\s+bank',
                    r'www\.kotak\.com',
                    r'kotak.*statement'
                ],
                'table_headers': ['date', 'description', 'chq/ref no', 'debit', 'credit', 'balance']
            },
            
            'CANARA': {
                'name': 'Canara Bank',
                'keywords': ['canara bank', 'canara'],
                'patterns': [
                    r'canara\s+bank',
                    r'www\.canarabank\.com',
                    r'canara.*statement'
                ],
                'table_headers': ['date', 'particulars', 'chq no', 'debit', 'credit', 'balance']
            },
            
            'PNB': {
                'name': 'Punjab National Bank (PNB)',
                'keywords': ['punjab national bank', 'pnb'],
                'patterns': [
                    r'punjab\s+national\s+bank',
                    r'\bpnb\b',
                    r'www\.pnbindia\.in',
                    r'pnb.*statement'
                ],
                'table_headers': ['date', 'particulars', 'chq/dd no', 'debit', 'credit', 'balance']
            },
            
            'UBI': {
                'name': 'Union Bank of India (UBI)',
                'keywords': ['union bank of india', 'ubi', 'union bank'],
                'patterns': [
                    r'union\s+bank\s+of\s+india',
                    r'\bubi\b',
                    r'www\.unionbankofindia\.co\.in',
                    r'union.*statement'
                ],
                'table_headers': ['date', 'particulars', 'chq/ref no', 'debit', 'credit', 'balance']
            },
            
            'BOI': {
                'name': 'Bank of India (BOI)',
                'keywords': ['bank of india', 'boi'],
                'patterns': [
                    r'bank\s+of\s+india',
                    r'\bboi\b',
                    r'www\.bankofindia\.co\.in',
                    r'star\s+connect'
                ],
                'table_headers': ['date', 'particulars', 'chq/ref no', 'debit', 'credit', 'balance']
            },
            
            'IDBI': {
                'name': 'IDBI Bank',
                'keywords': ['idbi bank', 'idbi', 'industrial development bank'],
                'patterns': [
                    r'idbi\s+bank',
                    r'industrial\s+development\s+bank',
                    r'www\.idbibank\.in'
                ],
                'table_headers': ['date', 'particulars', 'chq/ref no', 'debit', 'credit', 'balance']
            },
            
            'FEDERAL': {
                'name': 'Federal Bank',
                'keywords': ['federal bank', 'federal'],
                'patterns': [
                    r'federal\s+bank',
                    r'www\.federalbank\.co\.in',
                    r'federal.*statement'
                ],
                'table_headers': ['date', 'particulars', 'chq/ref no', 'debit', 'credit', 'balance']
            },
            
            'BANDHAN': {
                'name': 'Bandhan Bank',
                'keywords': ['bandhan bank', 'bandhan'],
                'patterns': [
                    r'bandhan\s+bank',
                    r'www\.bandhanbank\.com',
                    r'bandhan.*statement'
                ],
                'table_headers': ['date', 'particulars', 'chq/ref no', 'debit', 'credit', 'balance']
            },
            
            'IOB': {
                'name': 'Indian Overseas Bank (IOB)',
                'keywords': ['indian overseas bank', 'iob'],
                'patterns': [
                    r'indian\s+overseas\s+bank',
                    r'\biob\b',
                    r'www\.iob\.in'
                ],
                'table_headers': ['date', 'particulars', 'chq/ref no', 'debit', 'credit', 'balance']
            },
            
            'CBI': {
                'name': 'Central Bank of India (CBI)',
                'keywords': ['central bank of india', 'cbi', 'central bank'],
                'patterns': [
                    r'central\s+bank\s+of\s+india',
                    r'\bcbi\b',
                    r'www\.centralbankofindia\.co\.in'
                ],
                'table_headers': ['date', 'particulars', 'chq/ref no', 'debit', 'credit', 'balance']
            },
            
            'INDIAN': {
                'name': 'Indian Bank',
                'keywords': ['indian bank'],
                'patterns': [
                    r'indian\s+bank(?!\s+of)',  # 避免与Bank of India混淆
                    r'www\.indianbank\.in'
                ],
                'table_headers': ['date', 'particulars', 'chq/ref no', 'debit', 'credit', 'balance']
            },
            
            'UCO': {
                'name': 'UCO Bank',
                'keywords': ['uco bank', 'uco'],
                'patterns': [
                    r'uco\s+bank',
                    r'\buco\b',
                    r'www\.ucobank\.com'
                ],
                'table_headers': ['date', 'particulars', 'chq/ref no', 'debit', 'credit', 'balance']
            },
            
            'YES': {
                'name': 'YES Bank',
                'keywords': ['yes bank', 'yes'],
                'patterns': [
                    r'yes\s+bank',
                    r'\byes\b.*bank',
                    r'www\.yesbank\.in'
                ],
                'table_headers': ['date', 'description', 'chq/ref no', 'debit', 'credit', 'balance']
            },
            
            'SIB': {
                'name': 'South Indian Bank (SIB)',
                'keywords': ['south indian bank', 'sib'],
                'patterns': [
                    r'south\s+indian\s+bank',
                    r'\bsib\b',
                    r'www\.southindianbank\.com'
                ],
                'table_headers': ['date', 'particulars', 'chq/ref no', 'debit', 'credit', 'balance']
            },
            
            'INDUSIND': {
                'name': 'IndusInd Bank',
                'keywords': ['indusind bank', 'indusind'],
                'patterns': [
                    r'indusind\s+bank',
                    r'www\.indusind\.com'
                ],
                'table_headers': ['date', 'description', 'chq/ref no', 'debit', 'credit', 'balance']
            }
        }
    
    def extract_text_from_pdf(self, pdf_path: str) -> str:
        """
        从PDF中提取文本内容

        Args:
            pdf_path: PDF文件路径

        Returns:
            str: 提取的文本内容
        """
        text = ""

        # 方法1: 使用PyPDF2提取文本
        if PYPDF2_AVAILABLE:
            try:
                with open(pdf_path, 'rb') as file:
                    pdf_reader = PyPDF2.PdfReader(file)
                    for page_num in range(min(3, len(pdf_reader.pages))):  # 只读前3页
                        page = pdf_reader.pages[page_num]
                        text += page.extract_text() + "\n"
            except Exception as e:
                print(f"    ⚠️ PyPDF2提取失败: {e}")

        # 方法2: 使用tabula提取表格内容（作为补充）
        if TABULA_AVAILABLE and PANDAS_AVAILABLE:
            try:
                dfs = tabula.read_pdf(pdf_path, pages='1-3',
                                    pandas_options={'header': None, 'dtype': str})
                if dfs:
                    for df in dfs:
                        text += df.to_string() + "\n"
            except Exception as e:
                print(f"    ⚠️ tabula提取失败: {e}")

        # 方法3: 基于文件名的简单识别（作为后备方案）
        if not text.strip():
            filename = os.path.basename(pdf_path).lower()
            text = filename
            print(f"    ℹ️ 使用文件名作为识别依据: {filename}")

        return text.lower()  # 转换为小写便于匹配
    
    def calculate_bank_score(self, text: str, bank_code: str, bank_info: Dict) -> float:
        """
        计算银行匹配分数
        
        Args:
            text: PDF文本内容
            bank_code: 银行代码
            bank_info: 银行信息
            
        Returns:
            float: 匹配分数
        """
        score = 0.0
        
        # 1. 关键词匹配 (权重: 30%)
        keyword_score = 0
        for keyword in bank_info['keywords']:
            if keyword.lower() in text:
                keyword_score += 1
        
        if bank_info['keywords']:
            keyword_score = (keyword_score / len(bank_info['keywords'])) * 30
        
        # 2. 正则模式匹配 (权重: 40%)
        pattern_score = 0
        for pattern in bank_info['patterns']:
            if re.search(pattern, text, re.IGNORECASE):
                pattern_score += 1
        
        if bank_info['patterns']:
            pattern_score = (pattern_score / len(bank_info['patterns'])) * 40
        
        # 3. 表头结构匹配 (权重: 30%)
        header_score = 0
        for header in bank_info['table_headers']:
            if header.lower() in text:
                header_score += 1
        
        if bank_info['table_headers']:
            header_score = (header_score / len(bank_info['table_headers'])) * 30
        
        total_score = keyword_score + pattern_score + header_score
        
        return total_score
    
    def identify_bank(self, pdf_path: str) -> Tuple[Optional[str], Optional[str], float]:
        """
        识别PDF文件的银行类型
        
        Args:
            pdf_path: PDF文件路径
            
        Returns:
            Tuple[银行代码, 银行名称, 匹配分数]
        """
        print(f"  🔍 分析文件: {os.path.basename(pdf_path)}")
        
        # 提取PDF文本内容
        text = self.extract_text_from_pdf(pdf_path)
        
        if not text.strip():
            print(f"    ❌ 无法提取文本内容")
            return None, None, 0.0
        
        # 计算每个银行的匹配分数
        bank_scores = {}
        for bank_code, bank_info in self.bank_patterns.items():
            score = self.calculate_bank_score(text, bank_code, bank_info)
            bank_scores[bank_code] = score
            print(f"    📊 {bank_info['name']}: {score:.1f}分")
        
        # 找到最高分的银行
        if bank_scores:
            best_bank = max(bank_scores.items(), key=lambda x: x[1])
            bank_code, score = best_bank
            
            # 设置识别阈值
            threshold = 20.0  # 最低识别分数
            
            if score >= threshold:
                bank_name = self.bank_patterns[bank_code]['name']
                print(f"    ✅ 识别结果: {bank_name} (分数: {score:.1f})")
                return bank_code, bank_name, score
            else:
                print(f"    ❌ 分数过低，无法确定银行类型 (最高分: {score:.1f})")
                return None, None, score
        
        return None, None, 0.0
    
    def scan_directory(self, directory_path: str) -> List[Dict]:
        """
        扫描目录中的所有PDF文件并识别银行类型
        
        Args:
            directory_path: 目录路径
            
        Returns:
            List[Dict]: 识别结果列表
        """
        results = []
        pdf_files = []
        
        # 查找所有PDF文件
        for file_path in Path(directory_path).glob("*.pdf"):
            pdf_files.append(str(file_path))
        
        print(f"📁 找到 {len(pdf_files)} 个PDF文件")
        print("=" * 80)
        
        # 逐个识别
        for pdf_path in sorted(pdf_files):
            bank_code, bank_name, score = self.identify_bank(pdf_path)
            
            result = {
                'file_path': pdf_path,
                'file_name': os.path.basename(pdf_path),
                'bank_code': bank_code,
                'bank_name': bank_name,
                'score': score,
                'identified': bank_code is not None
            }
            
            results.append(result)
            print("-" * 80)
        
        return results
    
    def print_summary(self, results: List[Dict]):
        """
        打印识别结果摘要
        
        Args:
            results: 识别结果列表
        """
        print("\n" + "=" * 80)
        print("🏦 银行PDF文档识别结果汇总")
        print("=" * 80)
        
        identified_count = sum(1 for r in results if r['identified'])
        failed_count = len(results) - identified_count
        
        print(f"\n📊 统计信息:")
        print(f"  总文件数: {len(results)}")
        print(f"  成功识别: {identified_count}")
        print(f"  识别失败: {failed_count}")
        print(f"  识别率: {(identified_count/len(results)*100):.1f}%")
        
        print(f"\n📋 详细结果:")
        for result in results:
            status = "✅" if result['identified'] else "❌"
            bank_info = result['bank_name'] if result['identified'] else "未识别"
            score_info = f"({result['score']:.1f}分)" if result['score'] > 0 else ""
            
            print(f"  {status} {result['file_name']}")
            print(f"     -> {bank_info} {score_info}")
        
        # 按银行分组统计
        if identified_count > 0:
            bank_counts = {}
            for result in results:
                if result['identified']:
                    bank_name = result['bank_name']
                    bank_counts[bank_name] = bank_counts.get(bank_name, 0) + 1
            
            print(f"\n🏛️ 银行分布:")
            for bank_name, count in sorted(bank_counts.items()):
                print(f"  {bank_name}: {count} 个文件")


def main():
    """主函数"""
    print("🚀 启动银行PDF文档自动识别系统")
    print("=" * 80)
    
    # 目标目录
    target_directory = "files"
    
    if not os.path.exists(target_directory):
        print(f"❌ 目录不存在: {target_directory}")
        return
    
    # 创建识别器
    identifier = BankPDFIdentifier()
    
    # 扫描并识别
    results = identifier.scan_directory(target_directory)
    
    # 打印结果摘要
    identifier.print_summary(results)
    
    print(f"\n🎉 银行PDF识别完成！")


if __name__ == "__main__":
    main()
