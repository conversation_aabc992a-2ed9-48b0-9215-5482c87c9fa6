#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通用描述识别算法 - 自适应、无硬编码关键字
"""

import re
import pandas as pd
from typing import List, Tuple, Dict

class UniversalDescriptionExtractor:
    """
    通用描述提取器 - 基于内容特征的自适应算法
    
    核心原理：
    1. 内容特征分析：基于长度、字符类型、格式模式等特征
    2. 上下文关联：分析描述与Serial的位置关系
    3. 智能过滤：自动识别有效交易描述vs无关内容
    4. 自适应学习：根据数据模式动态调整识别策略
    """
    
    def __init__(self):
        # 无关内容模式（表头、分隔符等）
        self.noise_patterns = [
            r'^nan$',
            r'^-+$',  # 分隔线
            r'^=+$',  # 分隔线
            r'^\s*$', # 空白
            r'^(serial|transaction|value|description|cheque|debit|credit|balance).*$',  # 表头
        ]
        
        # 有效描述的特征模式
        self.valid_description_features = [
            'has_alphanumeric',     # 包含字母数字
            'has_special_chars',    # 包含特殊字符（/、@、:等）
            'reasonable_length',    # 合理长度（2-200字符）
            'contains_identifiers', # 包含标识符（数字序列、邮箱等）
            'financial_keywords',   # 金融相关词汇
        ]
    
    def is_noise_content(self, text: str) -> bool:
        """判断是否为无关内容"""
        text = text.strip().lower()
        
        for pattern in self.noise_patterns:
            if re.match(pattern, text, re.IGNORECASE):
                return True
        
        return False
    
    def analyze_content_features(self, text: str) -> Dict[str, bool]:
        """分析内容特征"""
        text = text.strip()
        
        features = {
            'has_alphanumeric': bool(re.search(r'[a-zA-Z]', text)) and bool(re.search(r'\d', text)),
            'has_special_chars': bool(re.search(r'[/@:.-]', text)),
            'reasonable_length': 2 <= len(text) <= 200,
            'contains_identifiers': bool(re.search(r'\d{6,}|@\w+|\w+\.\w+', text)),
            'financial_keywords': bool(re.search(r'(upi|imps|neft|rtgs|mbk|ach|atm|cash|transfer|payment|debit|credit|int\.pd|reversal|charges|fee)', text, re.IGNORECASE)),
            'has_date_pattern': bool(re.search(r'\d{2}-\d{2}-\d{4}|\d{4}-\d{2}-\d{2}', text)),
            'has_amount_pattern': bool(re.search(r'\d+\.\d{2}|\d+,\d+', text)),
        }
        
        return features
    
    def calculate_description_score(self, text: str, position_score: float = 1.0) -> float:
        """计算描述内容的可信度分数"""
        if self.is_noise_content(text):
            return 0.0
        
        features = self.analyze_content_features(text)
        
        # 基础分数计算
        score = 0.0
        
        # 核心特征权重
        if features['reasonable_length']:
            score += 2.0
        
        if features['has_alphanumeric']:
            score += 3.0
        
        if features['has_special_chars']:
            score += 2.0
        
        if features['contains_identifiers']:
            score += 3.0
        
        if features['financial_keywords']:
            score += 4.0
        
        # 额外特征加分
        if features['has_date_pattern']:
            score += 1.0
        
        if features['has_amount_pattern']:
            score += 1.0
        
        # 长度调整
        length = len(text.strip())
        if length < 5:
            score *= 0.5  # 过短内容降权
        elif length > 100:
            score *= 0.8  # 过长内容轻微降权
        
        # 位置权重
        score *= position_score
        
        return score
    
    def find_best_description_for_serial(self, serial_pos: int, nearby_descriptions: List[Tuple[int, str]], 
                                       serial_num: int) -> List[str]:
        """为特定Serial找到最佳描述"""
        if not nearby_descriptions:
            return []
        
        # 计算每个描述的分数
        scored_descriptions = []
        
        for pos, desc in nearby_descriptions:
            # 位置权重：距离越近权重越高
            distance = abs(pos - serial_pos)
            position_score = max(0.1, 1.0 - (distance * 0.1))
            
            # 计算内容分数
            content_score = self.calculate_description_score(desc, position_score)
            
            if content_score > 0:
                scored_descriptions.append((pos, desc, content_score, distance))
        
        if not scored_descriptions:
            return []
        
        # 按分数排序
        scored_descriptions.sort(key=lambda x: x[2], reverse=True)
        
        # 智能选择策略
        selected_descriptions = []
        
        # 选择最高分的描述
        best_desc = scored_descriptions[0]
        selected_descriptions.append(best_desc[1])
        
        # 如果最高分描述很短（可能是补充信息），尝试添加第二个描述
        if len(best_desc[1].strip()) <= 3 and len(scored_descriptions) > 1:
            second_best = scored_descriptions[1]
            if second_best[2] > 2.0:  # 第二个描述也有足够高的分数
                # 按位置顺序排列
                if best_desc[0] < second_best[0]:
                    selected_descriptions = [best_desc[1], second_best[1]]
                else:
                    selected_descriptions = [second_best[1], best_desc[1]]
        
        return selected_descriptions
    
    def extract_descriptions_for_serials(self, df: pd.DataFrame, serial_positions: List[Tuple[int, int, any]]) -> Dict[int, List[str]]:
        """为所有Serial提取描述"""
        print(f"    🧠 使用通用算法提取描述...")
        
        # 收集所有描述行
        description_rows = []
        for i, row in df.iterrows():
            desc = str(row.iloc[3]).strip() if len(row) > 3 else ""
            if desc and desc.lower() != 'nan':
                description_rows.append((i, desc))
        
        print(f"    📍 找到 {len(description_rows)} 个描述行")
        
        # 为每个Serial匹配描述
        serial_descriptions = {}
        
        for idx, (row_pos, serial_num, serial_row) in enumerate(serial_positions):
            print(f"    📍 处理Serial {serial_num} (行{row_pos})")
            
            # 检查Serial行本身是否有描述
            current_desc = str(serial_row.iloc[3]).strip() if len(serial_row) > 3 else ""
            if current_desc and current_desc.lower() != 'nan':
                # 单行模式
                serial_descriptions[serial_num] = [current_desc]
                print(f"      📝 单行模式: '{current_desc[:40]}...'")
                continue
            
            # 多行模式：在附近查找描述
            search_range = 5  # 扩大搜索范围
            nearby_descriptions = []
            
            for desc_pos, desc in description_rows:
                if abs(desc_pos - row_pos) <= search_range:
                    nearby_descriptions.append((desc_pos, desc))
            
            if nearby_descriptions:
                best_descriptions = self.find_best_description_for_serial(
                    row_pos, nearby_descriptions, serial_num
                )
                
                if best_descriptions:
                    serial_descriptions[serial_num] = best_descriptions
                    desc_preview = ' '.join(best_descriptions)[:60]
                    print(f"      📝 多行模式: '{desc_preview}...' (分数最高)")
                else:
                    print(f"      ⚠️ 未找到有效描述")
            else:
                print(f"      ⚠️ 搜索范围内无描述")
        
        return serial_descriptions

# 测试函数
def test_universal_algorithm():
    """测试通用算法"""
    extractor = UniversalDescriptionExtractor()
    
    # 测试各种描述类型
    test_descriptions = [
        "UPI/************/20:37:38/UPI/bharatpe.905000575",  # UPI交易
        "IMPS/P2A/************/XXXXXXXXXX7220/ok",           # IMPS交易
        "27740100010512:Int.Pd:01-07-2022 to 30-09-2022",   # 利息支付
        "ACH Debit/LIC OF INDIA/9095916700222",              # ACH借记
        "BY CASH",                                           # 现金交易
        "SELF",                                              # 自转账
        "TO TRANSFER",                                       # 转账
        "SMS Alert charges for Qtr Mar-22",                 # 费用
        "d",                                                 # 短字符
        "nan",                                               # 无效内容
        "-",                                                 # 分隔符
    ]
    
    print("🧪 测试通用算法对各种描述类型的识别能力:")
    print("="*60)
    
    for desc in test_descriptions:
        score = extractor.calculate_description_score(desc)
        features = extractor.analyze_content_features(desc)
        is_noise = extractor.is_noise_content(desc)
        
        print(f"描述: '{desc}'")
        print(f"  分数: {score:.2f}")
        print(f"  是否噪音: {is_noise}")
        print(f"  特征: {features}")
        print()

if __name__ == "__main__":
    test_universal_algorithm()
