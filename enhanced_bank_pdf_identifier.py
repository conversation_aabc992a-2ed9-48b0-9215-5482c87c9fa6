#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版银行PDF文档自动识别系统
集成文本特征识别 + LOGO图像识别的多模态融合方案
"""

import os
import re
import base64
import hashlib
from typing import Dict, List, Tuple, Optional, Union
from pathlib import Path
import json

# 尝试导入可选依赖
try:
    import tabula
    TABULA_AVAILABLE = True
except ImportError:
    TABULA_AVAILABLE = False
    print("⚠️ tabula-py未安装，将跳过表格提取功能")

try:
    import PyPDF2
    PYPDF2_AVAILABLE = True
except ImportError:
    PYPDF2_AVAILABLE = False
    print("⚠️ PyPDF2未安装，将跳过PDF文本提取功能")

try:
    import pandas as pd
    PANDAS_AVAILABLE = True
except ImportError:
    PANDAS_AVAILABLE = False
    print("⚠️ pandas未安装，将跳过数据处理功能")

try:
    import fitz  # PyMuPDF
    PYMUPDF_AVAILABLE = True
except ImportError:
    PYMUPDF_AVAILABLE = False
    print("⚠️ PyMuPDF未安装，将跳过图像提取功能")

try:
    from PIL import Image, ImageEnhance
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False
    print("⚠️ Pillow未安装，将跳过图像处理功能")

try:
    import pytesseract
    TESSERACT_AVAILABLE = True
except ImportError:
    TESSERACT_AVAILABLE = False
    print("⚠️ pytesseract未安装，将跳过OCR功能")

try:
    import cv2
    import numpy as np
    OPENCV_AVAILABLE = True
except ImportError:
    OPENCV_AVAILABLE = False
    print("⚠️ OpenCV未安装，将跳过高级图像处理功能")


class LogoImageProcessor:
    """
    LOGO图像处理器
    负责从PDF中提取图像并进行LOGO识别
    """
    
    def __init__(self):
        # 银行LOGO特征库（基于OCR文本识别）
        self.logo_text_patterns = {
            'SBI': [
                r'state\s+bank\s+of\s+india',
                r'\bsbi\b',
                r'भारतीय\s*स्टेट\s*बैंक',
                r'state\s*bank'
            ],
            'HDFC': [
                r'hdfc\s*bank',
                r'housing\s+development\s+finance',
                r'hdfc'
            ],
            'ICICI': [
                r'icici\s*bank',
                r'industrial\s+credit',
                r'icici'
            ],
            'BOB': [
                r'bank\s+of\s+baroda',
                r'baroda',
                r'\bbob\b'
            ],
            'KOTAK': [
                r'kotak\s*mahindra',
                r'kotak\s*bank',
                r'kotak'
            ],
            'CANARA': [
                r'canara\s*bank',
                r'canara'
            ],
            'PNB': [
                r'punjab\s+national\s+bank',
                r'\bpnb\b',
                r'punjab\s*national'
            ],
            'UBI': [
                r'union\s+bank\s+of\s+india',
                r'union\s*bank',
                r'\bubi\b'
            ],
            'BOI': [
                r'bank\s+of\s+india',
                r'\bboi\b',
                r'star\s*connect'
            ],
            'IDBI': [
                r'idbi\s*bank',
                r'industrial\s+development\s+bank',
                r'idbi'
            ],
            'FEDERAL': [
                r'federal\s*bank',
                r'federal'
            ],
            'BANDHAN': [
                r'bandhan\s*bank',
                r'bandhan'
            ],
            'IOB': [
                r'indian\s+overseas\s+bank',
                r'\biob\b',
                r'overseas\s*bank'
            ],
            'CBI': [
                r'central\s+bank\s+of\s+india',
                r'central\s*bank',
                r'\bcbi\b'
            ],
            'INDIAN': [
                r'indian\s*bank(?!\s+of)',
                r'indian\s*bank'
            ],
            'UCO': [
                r'uco\s*bank',
                r'\buco\b'
            ],
            'YES': [
                r'yes\s*bank',
                r'\byes\b'
            ],
            'SIB': [
                r'south\s+indian\s+bank',
                r'\bsib\b',
                r'south\s*indian'
            ],
            'INDUSIND': [
                r'indusind\s*bank',
                r'indusind'
            ]
        }
        
        # 图像特征哈希库（用于图像相似度匹配）
        self.logo_image_hashes = {}
        
    def extract_images_from_pdf(self, pdf_path: str) -> List[Image.Image]:
        """
        从PDF中提取图像
        
        Args:
            pdf_path: PDF文件路径
            
        Returns:
            List[Image.Image]: 提取的图像列表
        """
        images = []
        
        if not PYMUPDF_AVAILABLE or not PIL_AVAILABLE:
            print(f"    ⚠️ 图像提取功能不可用，跳过LOGO识别")
            return images
        
        try:
            # 使用PyMuPDF提取图像
            pdf_document = fitz.open(pdf_path)
            
            # 只处理前2页以提高性能
            max_pages = min(2, len(pdf_document))
            
            for page_num in range(max_pages):
                page = pdf_document[page_num]
                image_list = page.get_images()
                
                for img_index, img in enumerate(image_list):
                    try:
                        # 获取图像数据
                        xref = img[0]
                        pix = fitz.Pixmap(pdf_document, xref)
                        
                        # 转换为PIL Image
                        if pix.n - pix.alpha < 4:  # 确保是RGB或灰度图像
                            img_data = pix.tobytes("ppm")
                            pil_image = Image.open(io.BytesIO(img_data))
                            
                            # 过滤太小的图像（可能不是LOGO）
                            if pil_image.width >= 50 and pil_image.height >= 20:
                                images.append(pil_image)
                        
                        pix = None  # 释放内存
                        
                    except Exception as e:
                        print(f"    ⚠️ 提取图像{img_index}失败: {e}")
                        continue
            
            pdf_document.close()
            print(f"    📷 从PDF提取了 {len(images)} 个图像")
            
        except Exception as e:
            print(f"    ❌ PDF图像提取失败: {e}")
        
        return images
    
    def ocr_image_text(self, image: Image.Image) -> str:
        """
        使用OCR从图像中提取文本
        
        Args:
            image: PIL图像对象
            
        Returns:
            str: 提取的文本内容
        """
        if not TESSERACT_AVAILABLE:
            return ""
        
        try:
            # 图像预处理以提高OCR准确率
            # 转换为灰度图像
            if image.mode != 'L':
                image = image.convert('L')
            
            # 增强对比度
            enhancer = ImageEnhance.Contrast(image)
            image = enhancer.enhance(2.0)
            
            # 使用tesseract进行OCR
            text = pytesseract.image_to_string(image, lang='eng+hin')
            return text.lower().strip()
            
        except Exception as e:
            print(f"    ⚠️ OCR处理失败: {e}")
            return ""
    
    def calculate_image_hash(self, image: Image.Image) -> str:
        """
        计算图像的感知哈希值
        
        Args:
            image: PIL图像对象
            
        Returns:
            str: 图像哈希值
        """
        try:
            # 缩放到8x8像素
            image = image.convert('L').resize((8, 8), Image.Resampling.LANCZOS)
            
            # 计算平均像素值
            pixels = list(image.getdata())
            avg = sum(pixels) / len(pixels)
            
            # 生成哈希
            hash_bits = []
            for pixel in pixels:
                hash_bits.append('1' if pixel >= avg else '0')
            
            return ''.join(hash_bits)
            
        except Exception as e:
            print(f"    ⚠️ 图像哈希计算失败: {e}")
            return ""
    
    def identify_logo_by_ocr(self, images: List[Image.Image]) -> Dict[str, float]:
        """
        通过OCR识别LOGO中的银行名称
        
        Args:
            images: 图像列表
            
        Returns:
            Dict[str, float]: 各银行的OCR识别分数
        """
        bank_scores = {}
        
        if not images:
            return bank_scores
        
        print(f"    🔍 开始OCR LOGO识别...")
        
        # 合并所有图像的OCR文本
        all_ocr_text = ""
        for i, image in enumerate(images):
            ocr_text = self.ocr_image_text(image)
            if ocr_text:
                all_ocr_text += " " + ocr_text
                print(f"    📝 图像{i+1} OCR结果: {ocr_text[:50]}...")
        
        if not all_ocr_text.strip():
            print(f"    ⚠️ 未从图像中提取到文本")
            return bank_scores
        
        # 计算各银行的OCR匹配分数
        for bank_code, patterns in self.logo_text_patterns.items():
            score = 0
            matches = 0
            
            for pattern in patterns:
                if re.search(pattern, all_ocr_text, re.IGNORECASE):
                    matches += 1
                    score += 10  # 每个匹配的模式得10分
            
            if matches > 0:
                bank_scores[bank_code] = score
                print(f"    🎯 {bank_code}: OCR匹配 {matches} 个模式，得分 {score}")
        
        return bank_scores
    
    def identify_logo_by_similarity(self, images: List[Image.Image]) -> Dict[str, float]:
        """
        通过图像相似度匹配识别LOGO
        
        Args:
            images: 图像列表
            
        Returns:
            Dict[str, float]: 各银行的相似度分数
        """
        bank_scores = {}
        
        if not images or not self.logo_image_hashes:
            return bank_scores
        
        print(f"    🔍 开始图像相似度匹配...")
        
        # 计算输入图像的哈希值
        input_hashes = []
        for image in images:
            hash_value = self.calculate_image_hash(image)
            if hash_value:
                input_hashes.append(hash_value)
        
        # 与预设LOGO哈希进行匹配
        for bank_code, logo_hashes in self.logo_image_hashes.items():
            max_similarity = 0
            
            for input_hash in input_hashes:
                for logo_hash in logo_hashes:
                    # 计算汉明距离（相似度）
                    similarity = self._calculate_hash_similarity(input_hash, logo_hash)
                    max_similarity = max(max_similarity, similarity)
            
            if max_similarity > 0.7:  # 相似度阈值
                bank_scores[bank_code] = max_similarity * 20  # 转换为分数
                print(f"    🎯 {bank_code}: 图像相似度 {max_similarity:.2f}，得分 {bank_scores[bank_code]:.1f}")
        
        return bank_scores
    
    def _calculate_hash_similarity(self, hash1: str, hash2: str) -> float:
        """
        计算两个哈希值的相似度
        
        Args:
            hash1: 哈希值1
            hash2: 哈希值2
            
        Returns:
            float: 相似度 (0-1)
        """
        if len(hash1) != len(hash2):
            return 0.0
        
        # 计算汉明距离
        hamming_distance = sum(c1 != c2 for c1, c2 in zip(hash1, hash2))
        similarity = 1.0 - (hamming_distance / len(hash1))
        
        return similarity
    
    def process_pdf_images(self, pdf_path: str) -> Dict[str, float]:
        """
        处理PDF图像并返回LOGO识别结果
        
        Args:
            pdf_path: PDF文件路径
            
        Returns:
            Dict[str, float]: 各银行的图像识别分数
        """
        print(f"    🖼️ 开始图像LOGO识别...")
        
        # 提取图像
        images = self.extract_images_from_pdf(pdf_path)
        
        if not images:
            print(f"    ⚠️ 未提取到图像，跳过LOGO识别")
            return {}
        
        # OCR识别
        ocr_scores = self.identify_logo_by_ocr(images)
        
        # 图像相似度识别
        similarity_scores = self.identify_logo_by_similarity(images)
        
        # 融合OCR和相似度分数
        final_scores = {}
        all_banks = set(ocr_scores.keys()) | set(similarity_scores.keys())
        
        for bank_code in all_banks:
            ocr_score = ocr_scores.get(bank_code, 0)
            sim_score = similarity_scores.get(bank_code, 0)
            
            # 加权融合：OCR权重70%，相似度权重30%
            final_score = ocr_score * 0.7 + sim_score * 0.3
            
            if final_score > 0:
                final_scores[bank_code] = final_score
                print(f"    📊 {bank_code}: OCR({ocr_score:.1f}) + 相似度({sim_score:.1f}) = {final_score:.1f}")
        
        return final_scores


# 导入io模块
import io


class EnhancedBankPDFIdentifier:
    """
    增强版银行PDF识别器
    集成文本特征识别和LOGO图像识别的多模态融合方案
    """

    def __init__(self):
        # 初始化图像处理器
        self.logo_processor = LogoImageProcessor()

        # 银行识别规则库（继承原有的文本识别规则）
        self.bank_patterns = {
            'SBI': {
                'name': 'State Bank of India (SBI)',
                'keywords': ['state bank of india', 'sbi', 'भारतीय स्टेट बैंक'],
                'patterns': [
                    r'state\s+bank\s+of\s+india',
                    r'\bsbi\b',
                    r'account\s+statement.*sbi',
                    r'www\.sbi\.co\.in'
                ],
                'table_headers': ['transaction date', 'value date', 'description', 'ref no', 'debit', 'credit', 'balance']
            },

            'HDFC': {
                'name': 'HDFC Bank',
                'keywords': ['hdfc bank', 'hdfc', 'housing development finance'],
                'patterns': [
                    r'hdfc\s+bank',
                    r'housing\s+development\s+finance',
                    r'www\.hdfcbank\.com',
                    r'hdfc.*statement'
                ],
                'table_headers': ['date', 'narration', 'chq/ref no', 'value dt', 'withdrawal amt', 'deposit amt', 'closing balance']
            },

            'ICICI': {
                'name': 'ICICI Bank',
                'keywords': ['icici bank', 'icici', 'industrial credit'],
                'patterns': [
                    r'icici\s+bank',
                    r'industrial\s+credit\s+and\s+investment',
                    r'www\.icicibank\.com',
                    r'icici.*statement'
                ],
                'table_headers': ['date', 'particulars', 'cheque no', 'debit', 'credit', 'balance']
            },

            'BOB': {
                'name': 'Bank of Baroda (BOB)',
                'keywords': ['bank of baroda', 'bob', 'baroda'],
                'patterns': [
                    r'bank\s+of\s+baroda',
                    r'\bbob\b',
                    r'www\.bankofbaroda\.in',
                    r'baroda.*statement'
                ],
                'table_headers': ['serial no', 'transaction date', 'value date', 'description', 'cheque number', 'debit', 'credit', 'balance']
            },

            'KOTAK': {
                'name': 'Kotak Mahindra Bank',
                'keywords': ['kotak mahindra', 'kotak bank', 'kotak'],
                'patterns': [
                    r'kotak\s+mahindra\s+bank',
                    r'kotak\s+bank',
                    r'www\.kotak\.com',
                    r'kotak.*statement'
                ],
                'table_headers': ['date', 'description', 'chq/ref no', 'debit', 'credit', 'balance']
            },

            'CANARA': {
                'name': 'Canara Bank',
                'keywords': ['canara bank', 'canara'],
                'patterns': [
                    r'canara\s+bank',
                    r'www\.canarabank\.com',
                    r'canara.*statement'
                ],
                'table_headers': ['date', 'particulars', 'chq no', 'debit', 'credit', 'balance']
            },

            'PNB': {
                'name': 'Punjab National Bank (PNB)',
                'keywords': ['punjab national bank', 'pnb'],
                'patterns': [
                    r'punjab\s+national\s+bank',
                    r'\bpnb\b',
                    r'www\.pnbindia\.in',
                    r'pnb.*statement'
                ],
                'table_headers': ['date', 'particulars', 'chq/dd no', 'debit', 'credit', 'balance']
            },

            'UBI': {
                'name': 'Union Bank of India (UBI)',
                'keywords': ['union bank of india', 'ubi', 'union bank'],
                'patterns': [
                    r'union\s+bank\s+of\s+india',
                    r'\bubi\b',
                    r'www\.unionbankofindia\.co\.in',
                    r'union.*statement'
                ],
                'table_headers': ['date', 'particulars', 'chq/ref no', 'debit', 'credit', 'balance']
            },

            'BOI': {
                'name': 'Bank of India (BOI)',
                'keywords': ['bank of india', 'boi'],
                'patterns': [
                    r'bank\s+of\s+india',
                    r'\bboi\b',
                    r'www\.bankofindia\.co\.in',
                    r'star\s+connect'
                ],
                'table_headers': ['date', 'particulars', 'chq/ref no', 'debit', 'credit', 'balance']
            },

            'IDBI': {
                'name': 'IDBI Bank',
                'keywords': ['idbi bank', 'idbi', 'industrial development bank'],
                'patterns': [
                    r'idbi\s+bank',
                    r'industrial\s+development\s+bank',
                    r'www\.idbibank\.in'
                ],
                'table_headers': ['date', 'particulars', 'chq/ref no', 'debit', 'credit', 'balance']
            },

            'FEDERAL': {
                'name': 'Federal Bank',
                'keywords': ['federal bank', 'federal'],
                'patterns': [
                    r'federal\s+bank',
                    r'www\.federalbank\.co\.in',
                    r'federal.*statement'
                ],
                'table_headers': ['date', 'particulars', 'chq/ref no', 'debit', 'credit', 'balance']
            },

            'BANDHAN': {
                'name': 'Bandhan Bank',
                'keywords': ['bandhan bank', 'bandhan'],
                'patterns': [
                    r'bandhan\s+bank',
                    r'www\.bandhanbank\.com',
                    r'bandhan.*statement'
                ],
                'table_headers': ['date', 'particulars', 'chq/ref no', 'debit', 'credit', 'balance']
            },

            'IOB': {
                'name': 'Indian Overseas Bank (IOB)',
                'keywords': ['indian overseas bank', 'iob'],
                'patterns': [
                    r'indian\s+overseas\s+bank',
                    r'\biob\b',
                    r'www\.iob\.in'
                ],
                'table_headers': ['date', 'particulars', 'chq/ref no', 'debit', 'credit', 'balance']
            },

            'CBI': {
                'name': 'Central Bank of India (CBI)',
                'keywords': ['central bank of india', 'cbi', 'central bank'],
                'patterns': [
                    r'central\s+bank\s+of\s+india',
                    r'\bcbi\b',
                    r'www\.centralbankofindia\.co\.in'
                ],
                'table_headers': ['date', 'particulars', 'chq/ref no', 'debit', 'credit', 'balance']
            },

            'INDIAN': {
                'name': 'Indian Bank',
                'keywords': ['indian bank'],
                'patterns': [
                    r'indian\s+bank(?!\s+of)',  # 避免与Bank of India混淆
                    r'www\.indianbank\.in'
                ],
                'table_headers': ['date', 'particulars', 'chq/ref no', 'debit', 'credit', 'balance']
            },

            'UCO': {
                'name': 'UCO Bank',
                'keywords': ['uco bank', 'uco'],
                'patterns': [
                    r'uco\s+bank',
                    r'\buco\b',
                    r'www\.ucobank\.com'
                ],
                'table_headers': ['date', 'particulars', 'chq/ref no', 'debit', 'credit', 'balance']
            },

            'YES': {
                'name': 'YES Bank',
                'keywords': ['yes bank', 'yes'],
                'patterns': [
                    r'yes\s+bank',
                    r'\byes\b.*bank',
                    r'www\.yesbank\.in'
                ],
                'table_headers': ['date', 'description', 'chq/ref no', 'debit', 'credit', 'balance']
            },

            'SIB': {
                'name': 'South Indian Bank (SIB)',
                'keywords': ['south indian bank', 'sib'],
                'patterns': [
                    r'south\s+indian\s+bank',
                    r'\bsib\b',
                    r'www\.southindianbank\.com'
                ],
                'table_headers': ['date', 'particulars', 'chq/ref no', 'debit', 'credit', 'balance']
            },

            'INDUSIND': {
                'name': 'IndusInd Bank',
                'keywords': ['indusind bank', 'indusind'],
                'patterns': [
                    r'indusind\s+bank',
                    r'www\.indusind\.com'
                ],
                'table_headers': ['date', 'description', 'chq/ref no', 'debit', 'credit', 'balance']
            }
        }

    def extract_text_from_pdf(self, pdf_path: str) -> str:
        """
        从PDF中提取文本内容

        Args:
            pdf_path: PDF文件路径

        Returns:
            str: 提取的文本内容
        """
        text = ""

        # 方法1: 使用PyPDF2提取文本
        if PYPDF2_AVAILABLE:
            try:
                with open(pdf_path, 'rb') as file:
                    pdf_reader = PyPDF2.PdfReader(file)
                    for page_num in range(min(3, len(pdf_reader.pages))):  # 只读前3页
                        page = pdf_reader.pages[page_num]
                        text += page.extract_text() + "\n"
            except Exception as e:
                print(f"    ⚠️ PyPDF2提取失败: {e}")

        # 方法2: 使用tabula提取表格内容（作为补充）
        if TABULA_AVAILABLE and PANDAS_AVAILABLE:
            try:
                dfs = tabula.read_pdf(pdf_path, pages='1-3',
                                    pandas_options={'header': None, 'dtype': str})
                if dfs:
                    for df in dfs:
                        text += df.to_string() + "\n"
            except Exception as e:
                print(f"    ⚠️ tabula提取失败: {e}")

        # 方法3: 基于文件名的简单识别（作为后备方案）
        if not text.strip():
            filename = os.path.basename(pdf_path).lower()
            text = filename
            print(f"    ℹ️ 使用文件名作为识别依据: {filename}")

        return text.lower()  # 转换为小写便于匹配

    def calculate_text_score(self, text: str, bank_code: str, bank_info: Dict) -> float:
        """
        计算文本特征匹配分数

        Args:
            text: PDF文本内容
            bank_code: 银行代码
            bank_info: 银行信息

        Returns:
            float: 文本匹配分数
        """
        score = 0.0

        # 1. 关键词匹配 (权重: 30%)
        keyword_score = 0
        for keyword in bank_info['keywords']:
            if keyword.lower() in text:
                keyword_score += 1

        if bank_info['keywords']:
            keyword_score = (keyword_score / len(bank_info['keywords'])) * 30

        # 2. 正则模式匹配 (权重: 40%)
        pattern_score = 0
        for pattern in bank_info['patterns']:
            if re.search(pattern, text, re.IGNORECASE):
                pattern_score += 1

        if bank_info['patterns']:
            pattern_score = (pattern_score / len(bank_info['patterns'])) * 40

        # 3. 表头结构匹配 (权重: 30%)
        header_score = 0
        for header in bank_info['table_headers']:
            if header.lower() in text:
                header_score += 1

        if bank_info['table_headers']:
            header_score = (header_score / len(bank_info['table_headers'])) * 30

        total_score = keyword_score + pattern_score + header_score

        return total_score

    def identify_bank_multimodal(self, pdf_path: str) -> Tuple[Optional[str], Optional[str], Dict[str, float]]:
        """
        多模态银行识别（文本+图像融合）

        Args:
            pdf_path: PDF文件路径

        Returns:
            Tuple[银行代码, 银行名称, 详细分数信息]
        """
        print(f"  🔍 多模态分析文件: {os.path.basename(pdf_path)}")

        # 1. 文本特征识别
        print(f"    📝 开始文本特征识别...")
        text = self.extract_text_from_pdf(pdf_path)

        text_scores = {}
        if text.strip():
            for bank_code, bank_info in self.bank_patterns.items():
                score = self.calculate_text_score(text, bank_code, bank_info)
                text_scores[bank_code] = score

        # 2. 图像LOGO识别
        print(f"    🖼️ 开始图像LOGO识别...")
        image_scores = self.logo_processor.process_pdf_images(pdf_path)

        # 3. 多模态融合
        print(f"    🔄 开始多模态融合...")
        final_scores = {}
        all_banks = set(text_scores.keys()) | set(image_scores.keys())

        for bank_code in all_banks:
            text_score = text_scores.get(bank_code, 0)
            image_score = image_scores.get(bank_code, 0)

            # 加权融合：文本权重60%，图像权重40%
            final_score = text_score * 0.6 + image_score * 0.4

            if final_score > 0:
                final_scores[bank_code] = final_score

        # 4. 显示详细分数
        score_details = {}
        for bank_code in all_banks:
            text_score = text_scores.get(bank_code, 0)
            image_score = image_scores.get(bank_code, 0)
            final_score = final_scores.get(bank_code, 0)

            if final_score > 0:
                bank_name = self.bank_patterns[bank_code]['name']
                score_details[bank_code] = {
                    'name': bank_name,
                    'text_score': text_score,
                    'image_score': image_score,
                    'final_score': final_score
                }
                print(f"    📊 {bank_name}: 文本({text_score:.1f}) + 图像({image_score:.1f}) = 最终({final_score:.1f})")

        # 5. 确定最终识别结果
        if final_scores:
            best_bank = max(final_scores.items(), key=lambda x: x[1])
            bank_code, score = best_bank

            # 设置识别阈值
            threshold = 15.0  # 降低阈值以提高识别率

            if score >= threshold:
                bank_name = self.bank_patterns[bank_code]['name']
                print(f"    ✅ 识别结果: {bank_name} (最终分数: {score:.1f})")
                return bank_code, bank_name, score_details
            else:
                print(f"    ❌ 分数过低，无法确定银行类型 (最高分: {score:.1f})")
                return None, None, score_details

        print(f"    ❌ 未找到匹配的银行")
        return None, None, score_details

    def scan_directory_enhanced(self, directory_path: str) -> List[Dict]:
        """
        增强版目录扫描，使用多模态识别

        Args:
            directory_path: 目录路径

        Returns:
            List[Dict]: 增强版识别结果列表
        """
        results = []
        pdf_files = []

        # 查找所有PDF文件
        for file_path in Path(directory_path).glob("*.pdf"):
            pdf_files.append(str(file_path))

        print(f"📁 找到 {len(pdf_files)} 个PDF文件")
        print("=" * 80)

        # 逐个识别
        for pdf_path in sorted(pdf_files):
            bank_code, bank_name, score_details = self.identify_bank_multimodal(pdf_path)

            result = {
                'file_path': pdf_path,
                'file_name': os.path.basename(pdf_path),
                'bank_code': bank_code,
                'bank_name': bank_name,
                'identified': bank_code is not None,
                'score_details': score_details,
                'final_score': score_details.get(bank_code, {}).get('final_score', 0) if bank_code else 0
            }

            results.append(result)
            print("-" * 80)

        return results

    def print_enhanced_summary(self, results: List[Dict]):
        """
        打印增强版识别结果摘要

        Args:
            results: 识别结果列表
        """
        print("\n" + "=" * 80)
        print("🏦 增强版银行PDF文档识别结果汇总")
        print("=" * 80)

        identified_count = sum(1 for r in results if r['identified'])
        failed_count = len(results) - identified_count

        print(f"\n📊 统计信息:")
        print(f"  总文件数: {len(results)}")
        print(f"  成功识别: {identified_count}")
        print(f"  识别失败: {failed_count}")
        print(f"  识别率: {(identified_count/len(results)*100):.1f}%")

        # 与原版本对比
        original_success = 17  # 原版本成功识别数
        improvement = identified_count - original_success
        if improvement > 0:
            print(f"  🚀 相比原版本提升: +{improvement} 个文件 (+{(improvement/len(results)*100):.1f}%)")

        print(f"\n📋 详细结果:")
        for result in results:
            status = "✅" if result['identified'] else "❌"
            bank_info = result['bank_name'] if result['identified'] else "未识别"

            # 显示详细分数信息
            if result['identified'] and result['bank_code'] in result['score_details']:
                details = result['score_details'][result['bank_code']]
                score_info = f"(文本:{details['text_score']:.1f} + 图像:{details['image_score']:.1f} = {details['final_score']:.1f})"
            else:
                score_info = ""

            print(f"  {status} {result['file_name']}")
            print(f"     -> {bank_info} {score_info}")

        # 失败文件分析
        if failed_count > 0:
            print(f"\n🔍 识别失败文件分析:")
            for result in results:
                if not result['identified']:
                    print(f"  ❌ {result['file_name']}")

                    # 显示最高分的银行（即使未达到阈值）
                    if result['score_details']:
                        best_bank = max(result['score_details'].items(),
                                      key=lambda x: x[1]['final_score'])
                        bank_code, details = best_bank
                        print(f"     最接近: {details['name']} (分数: {details['final_score']:.1f})")
                        print(f"     分解: 文本({details['text_score']:.1f}) + 图像({details['image_score']:.1f})")
                    else:
                        print(f"     原因: 无法提取有效特征")

        # 按银行分组统计
        if identified_count > 0:
            bank_counts = {}
            for result in results:
                if result['identified']:
                    bank_name = result['bank_name']
                    bank_counts[bank_name] = bank_counts.get(bank_name, 0) + 1

            print(f"\n🏛️ 银行分布:")
            for bank_name, count in sorted(bank_counts.items()):
                print(f"  {bank_name}: {count} 个文件")

        # 性能分析
        print(f"\n📈 性能分析:")
        text_only_success = sum(1 for r in results if r['identified'] and
                               r['score_details'].get(r['bank_code'], {}).get('image_score', 0) == 0)
        image_helped = identified_count - text_only_success

        print(f"  仅文本识别成功: {text_only_success} 个")
        print(f"  图像识别辅助成功: {image_helped} 个")
        if image_helped > 0:
            print(f"  🎯 图像识别贡献率: {(image_helped/identified_count*100):.1f}%")


def main():
    """主函数"""
    print("🚀 启动增强版银行PDF文档自动识别系统")
    print("🔧 集成文本特征识别 + LOGO图像识别的多模态融合方案")
    print("=" * 80)

    # 目标目录
    target_directory = "files"

    if not os.path.exists(target_directory):
        print(f"❌ 目录不存在: {target_directory}")
        return

    # 创建增强版识别器
    identifier = EnhancedBankPDFIdentifier()

    # 扫描并识别
    results = identifier.scan_directory_enhanced(target_directory)

    # 打印结果摘要
    identifier.print_enhanced_summary(results)

    print(f"\n🎉 增强版银行PDF识别完成！")


if __name__ == "__main__":
    main()
