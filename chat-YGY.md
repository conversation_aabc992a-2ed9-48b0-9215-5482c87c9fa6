还是有问题
1、有的数据并不止跨越 Cheque No 列 他还跨越到 Withdrawal 列，所以还是有些数据被截断了
2、你虽然合并了 Cheque No 列的内容，但是合并完的内容不连贯，中间有空白字符
3、你不能随意给我删除列，我要保留原有的所有列和列名
基于以上问题再优化一下

还是有问题
1、首先行数你少了一行
2、Withdrawal(in Rs.) Deposits(in Rs.) Balance(in Rs.) 这三列的数据也不对 有些串掉了
你再仔细对比看看，再优化一下

还是有问题
你看看 Sl No 是 52 那行数据 Withdrawal(in Rs.) Deposits(in Rs.) 两列数据是不是跟 check 文件不一致
看起来 Withdrawal(in Rs.) Deposits(in Rs.)这两列数据的提取逻辑还是有问题，我看你是通过关键字来分类的，但是这个文档中可能并没有覆盖到完整的关键字，后面新的账单可能有别的关键字，这种分类方法是不是不太严谨，我要的是通用的逻辑而不是仅仅只针对该文档的

还有你的对比方法是不是有问题，没完全比对 可能也要再优化一下

agmentcode
第一次

参考其他实现，帮我完成 boi 的解析，目前遇到的几个问题
1、如果使用 tabula 解析，因为 Description 列数据并不止跨越 Cheque No 列 他还跨越到 Withdrawal 列，所以会被截断，导致数据不对
2、如果使用 pypdf 解析，因为无法还原原来的列顺序，导致 Withdrawal(in Rs.)和 Deposits(in Rs.)这两列数据无法正确分类

所以需要结合 tabula 和 pypdf 的优势来实现，参考下面的实现方式，具体步骤如下

1. `tabula`负责结构和金额：

    - 使用 tabula.read_pdf（lattice=True 模式）提取表格，它能较好地保留列结构。
    - 识别并设置正确的表头，清理非数据行。
    - 清洗 Withdrawal、Deposits 和 Balance 列的数值。
    - 这将得到一个 df_structured，其中金额列是准确的，但 Description 列可能仍有文本问题。

2. `pypdf`负责干净描述：

    - 使用 pypdf 提取 PDF 的原始文本。
    - 利用正则表达式，从原始文本中精确提取 Sl No、Txn Date 和完整正确的的`Description`。

3. 合并数据：
    - 遍历 df_structured 的每一行。
    - 根据 Sl No 和 Txn Date，在 pypdf 提取的完整的描述中找到对应项。
    - 用完整的描述替换 df_structured 中对应行的 Description 列。

需要注意的点
1、Description 列比较复杂，存在跨列情况，甚至出现跨两列的情况。
2、需要注意 Cheque No 列的数据，它可能不是自己的数据，而是跨列的数据，所以需要清理掉。

第二次

还有以下问题需要优化，
1、SI No 列的值不对，参考文件里面是是整数，而你解析出来的是带小数点的
2、比如第 10 行原始文件描述是 2 行，而你解析出来却只有第 1 行的数据，第二行的数据丢了
3、比如第 19 行，原始文件描述是 2 行，需要解析出来拼接成一行就行，而不是还是 2 行
4、Cheque No 的清洗逻辑不严谨，我给个方案，先解决保证 Cheque No 列解析出来的字符正确（即不会加入空白字符），然后判断这个字符是不是描述中的一部分来判断是否要清洗 Cheque No 列的数据，而不是通过关键字判断
5、你参考 icici 的 validate_results.py 也实现一个脚本来判断解析结果的正确性

第三次

还有问题
1、SI No 是 10 的这行数据的 Description，有错误，你解析出来的是 UPI/220934839382/DR/Profes/UTIB/982232945/Description, 而正确的字符是 check 文件里面的 UPI/220934839382/DR/Profes/UTIB/982232945/DSC CL
2、Cheque No 列里面还有很多行有错误的数据，这些错误在 check 文件中是不存在的，还有这些数据有个共同的特点就是去掉空白字符后都是 Description 的一部分，这部分需要优化
3、你实现的 validate_results.py 为什么没有检查出我上面说的两个问题？我需要实现的验证是 100%跟 check 文件一致才可以·

你先理解一下该项目，并仔细分析下@boi 目录，然后参考 boi 实现的解析策略(结合 tabula 和 pypdf 的优势来实现,混合策略)帮我实现 bandhan 银行账单(@files/13-bandhan-*********-Statement-Bandhan-Bank.pdf)的解析
1、要求在 bandhan 目录实现，在该目录下生成 bandhan_extracted.csv bandhan_extracted.json bandhan_extracted.xlsx 三个文件，并生成解析报告
2、要保留 bandban PDF 银行账单里面原始的表格格式，你只是参考 boi 的实现策略和方法
3、你不能随意给我删除列，我要保留 PDF 中原有的所有列和列名
4、如果你觉得格式比较规范用 tabula 就可以实现，你可以单独使用 tabula 就好，不用结合两者
5、因为 bandhan 目前没有可对比的文件，所以你先不用实现验证逻辑

你先理解一下该项目，并仔细分析下@boi 目录，然后参考 boi 实现的解析策略(结合 tabula 和 pypdf 的优势来实现,混合策略)帮我实现 iob 银行账单(@14-iob-*********-Statement-***************-2.pdf)的解析
1、要求在 iob 目录实现，在该目录下生成 iob_extracted.csv iob_extracted.json iob_extracted.xlsx 三个文件，并生成解析报告
2、要保留 iob PDF 银行账单里面原始的表格格式，你只是参考 boi 的实现策略和方法
3、你不能随意给我删除列，我要保留 PDF 中原有的所有列和列名
4、如果你觉得格式比较规范用 tabula 就可以实现，你可以单独使用 tabula 就好，不用结合两者
5、因为 iob 目前没有可对比的文件，所以你先不用实现验证逻辑

你先理解一下该项目，并仔细分析下@boi 目录，然后参考 boi 实现的解析策略(结合 tabula 和 pypdf 的优势来实现,混合策略)帮我实现 indian 银行账单(@16-indian-*********-SOORAJ-INDIAN-BANK-STATEMENT.pdf.crdownload.pdf)的解析
1、要求在 indian 目录实现，在该目录下生成 indian_extracted.csv indian_extracted.json indian_extracted.xlsx 三个文件，并生成解析报告
2、要保留 indian PDF 银行账单里面原始的表格格式，你只是参考 boi 的实现策略和方法
3、你不能随意给我删除列，我要保留 PDF 中原有的所有列和列名
4、如果你觉得格式比较规范用 tabula 就可以实现，你可以单独使用 tabula 就好，不用结合两者
5、因为 indian 目前没有可对比的文件，所以你先不用实现验证逻辑
6、要注意不要出现 iob 解析中按日期匹配导致数据被覆盖的问题

修复
2 个问题
1、Remarks 列大部分数据不对，不完整，第二行数据丢失了
2、Amount (Rs.) 列的数据只剩下金额了，请给我保留原来的数据比如，6400.00 (Dr) 或者 6500.00 (Cr)
继续优化一下
=>
Indian Bank PDF 解析器存在两个关键数据质量问题需要修复：

**问题 1 - Remarks 列数据不完整：**

-   检查生成的 indian_extracted.csv 文件，发现 Remarks 列大部分数据不完整
-   具体问题：第二行数据显示为"UPIAB/************/CR/AVINA"，但原始 PDF 中应该包含更完整的描述信息
-   原因分析：PyPDF 文本提取或数据合并过程中可能存在截断问题

**问题 2 - Amount (Rs.)列格式丢失：**

-   当前 Amount (Rs.)列只显示纯数字（如：6400.0, 6500.0）
-   要求保留原始 PDF 中的完整格式，包括借贷标识：
    -   借记格式：6400.00 (Dr)
    -   贷记格式：6500.00 (Cr)
-   这个格式信息对于区分交易类型非常重要

**修复要求：**

1. 分析并修复 Remarks 列数据提取逻辑，确保获取完整的交易描述
2. 修改 Amount (Rs.)列的数据处理逻辑，保留原始的金额格式（包含 Dr/Cr 标识）
3. 重新运行解析器并验证修复效果
4. 确保修复后的数据与原始 PDF 中的信息完全一致

**验证标准：**

-   Remarks 列应包含完整的交易描述信息
-   Amount (Rs.)列应保持"金额 (Dr/Cr)"的原始格式
-   所有交易记录的数据完整性和准确性

继续实现一下 uco 银行的账单 文件是@17-uco-*********-Account-Statement-UCO-Dec23-May24.pdf
1、要求在 uco 目录实现，在该目录下生成 uco_extracted.csv uco_extracted.json uco_extracted.xlsx 三个文件，并生成解析报告
2、要保留 uco PDF 银行账单里面原始的表格格式
3、你不能随意给我删除列，我要保留 PDF 中原有的所有列和列名
4、如果你觉得最好的实现方式来实现
5、因为 uco 目前没有可对比的文件，所以你先不用实现验证逻辑

条数不对，pdf 有 4 页数据，每页都几十条了，你检查反思一下，再优化一下

最后一页数据还是不对，你重新实现整个提取逻辑
额外需要注意的点
1、这个账单只有第一页有表头，其他页没有表头
2、后面每页的列数是一样的，只不过有的列没有数据

还有问题 最后一页的 Withdrawals 列的数据，你都把它解析到 Particulars 列了，你再检查下
还有我提出质疑 第 2，3 页数据你都能识别出来，为什么最后一页你识别不出来正确数据呢？pdf 数据结构是一样的，是不是你的实现方式上哪个差异导致的呢

UCO 银行 只保留最新的实现方式，和其他银行一样的命名，旧的文件删除，解析结果要生成出来

我看到你处理解析的两个关键地方：

# 第 2-3 页：无表头的标准页面

# 第 4 页：特殊处理

这种处理方式非常不通用，你现在的解析逻辑完全是基于我提供的银行账单文件，没有考虑普遍适用逻辑，如果我下次提供总共只有 3 页 5 页 6 页的账单文件怎么办？
请充分考虑适用性问题后再重新优化下，我需要的是解析脚本是能够解析该银行的其他账单，而不是只有当前这个账单文件

继续实现一下 yes 银行的账单 文件是@18-yes-*********-YES-BANK-1-1-MARCH-2023-TO-4-APRIL-2023.pdf
1、要求在 yes 目录实现，在该目录下生成 yes_extracted.csv yes_extracted.json yes_extracted.xlsx 三个文件，并生成解析报告
2、要保留 yes PDF 银行账单里面原始的表格格式
3、你不能随意给我删除列，我要保留 PDF 中原有的所有列和列名
4、如果你觉得最好的实现方式来实现
5、因为 yes 目前没有可对比的文件，所以你先不用实现验证逻辑
6、实现的解析逻辑要考虑通用性，可以适用该银行其他月份的账单文件，而不是针对当前这个文件来实现

继续实现一下 cbi 银行的账单 文件是@15-cbi-*********-Bank-statement.pdf
1、要求在 cbi 目录实现，在该目录下生成 cbi_extracted.csv cbi_extracted.json cbi_extracted.xlsx 三个文件，并生成解析报告
2、要保留 cbi PDF 银行账单里面原始的表格格式
3、你不能随意给我删除列，我要保留 PDF 中原有的所有列和列名
4、如果你觉得最好的实现方式来实现
5、因为 cbi 目前没有可对比的文件，所以你先不用实现验证逻辑
6、实现的解析逻辑要考虑通用性，可以适用该银行其他月份的账单文件，而不是针对当前这个文件来实现
7、这个文件的格式比较特殊有几点需要注意
a、每页开头都有一行 BROUGHT FORWARD :
b、每页结尾都有一行 CARRIED FORWARD :
c、BROUGHT FORWARD 和 CARRIED FORWARD 之间的部分才是真正有效的数据
d、第二列 Post Date 和第三列 Details 数据看起来交叉在一起了，用表格分割法看起来行不通
e、第三例 Details 数据存在多行情况，而且可能跨越多页，比如可能出现有一行在上一页，剩下的行在下一页的情况

效果非常不好
它的表头总的有 7 列，用一长一短两个横线包围，然后表格数据在下方，结尾还是一长一短的横线
比如
——————————————————
——————————
表头
——————————————————
——————————
表格数据
——————————————————
——————————

另外除了 BROUGHT FORWARD 和 CARRIED FORWARD 不是转账记录， CLOSING BALANCE :是整个文件的最后一行 也不是转账记录
这个表格有页码标识，总的有 33 页有数据
Balance 列要求保留原来的字符，比如 18,569.66Cr Cr 要保留

第一条数据就错了，正确格式如下
Value Date
02/10/21
Post Date
02/10/21
Details
TO TRF. TPCapfrst IDFC FIRST CBI TRF TO ***********
Cha.No.

Debit
3,847.00 1,
Credit

Balance
678.46Cr

还有 Details 出现多条 “In Case Your Account Is xxxx”这部分并不属于表格内容，你分析下原来的结构优化一下

效果依然不好
我给个方案
1、Value Date，Debit，Credit，Balance 这四列可以使用 tabula 分割解析出来
2、因为 Post Date 和 Details 合并成一列了 所以用正则来识别，一列是日期，一列是文本，Details 可能存在多行，需要做合并处理，换行的前置就没日期字符了这个是个有效规则
3、注意 BROUGHT FORWARD 和 CARRIED FORWARD 以及 CLOSING BALANCE 不是转账记录，属于页面开头或总结，需要排除
4、每一页表头前面都有一部分总结文字，这部分也需要排除

继续实现一下 bob 银行的账单 文件是@8-bob-*********-BOB-STATEMENT-page-1-2.pdf
1、要求在 bob 目录实现，在该目录下生成 bob_extracted.csv bob_extracted.json bob_extracted.xlsx 三个文件，并生成解析报告
2、要保留 bob PDF 银行账单里面原始的表格格式
3、你不能随意给我删除列，我要保留 PDF 中原有的所有列和列名
4、如果你觉得最好的实现方式来实现
5、因为 bob 目前没有可对比的文件，所以你先不用实现验证逻辑
6、实现的解析逻辑要考虑通用性，可以适用该银行其他月份的账单文件，而不是针对当前这个文件来实现

两个问题
1、Serial No 是 1 的那行数据丢失了。
2、Description 列的解析完全错乱了，Description 列存在多行情况，他跟日期在横轴上并不是上对齐，而是中间对齐。所以你在进行分割的时候可能出现错行的情况，也就是可能把本来是第三行的数据归类到第二行了。这一点你再优化下。


行三
UPI/130515257828/15:09:48/UPI/adilshaikh8735@okh
d
行四
UPI/130658724392/11:17:45/UPI/7046825565@ybl/Pa
ym