#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整增强版银行PDF文档自动识别系统
集成文本特征识别 + LOGO图像识别的多模态融合方案
"""

import os
import re
import io
from typing import Dict, List, Tuple, Optional
from pathlib import Path

# 导入依赖
try:
    import PyPDF2
    PYPDF2_AVAILABLE = True
    print("✅ PyPDF2可用")
except ImportError:
    PYPDF2_AVAILABLE = False
    print("❌ PyPDF2不可用")

try:
    import fitz  # PyMuPDF
    PYMUPDF_AVAILABLE = True
    print("✅ PyMuPDF可用")
except ImportError:
    PYMUPDF_AVAILABLE = False
    print("❌ PyMuPDF不可用")

try:
    from PIL import Image, ImageEnhance
    PIL_AVAILABLE = True
    print("✅ Pillow可用")
except ImportError:
    PIL_AVAILABLE = False
    print("❌ Pillow不可用")

try:
    import pytesseract
    TESSERACT_AVAILABLE = True
    print("✅ pytesseract可用")
except ImportError:
    TESSERACT_AVAILABLE = False
    print("❌ pytesseract不可用")


class FullEnhancedBankPDFIdentifier:
    """
    完整增强版银行PDF识别器
    集成文本特征识别和LOGO图像识别的多模态融合方案
    """
    
    def __init__(self):
        # 银行识别规则库
        self.bank_patterns = {
            'SBI': {
                'name': 'State Bank of India (SBI)',
                'keywords': ['state bank of india', 'sbi', 'भारतीय स्टेट बैंक', 'state bank'],
                'patterns': [
                    r'state\s+bank\s+of\s+india',
                    r'\bsbi\b',
                    r'state\s+bank',
                    r'www\.sbi\.co\.in',
                    r'भारतीय\s*स्टेट\s*बैंक'
                ],
                'logo_patterns': [
                    r'state\s*bank',
                    r'\bsbi\b',
                    r'भारतीय.*बैंक'
                ],
                'priority_weight': 1.2
            },
            
            'HDFC': {
                'name': 'HDFC Bank',
                'keywords': ['hdfc bank', 'hdfc', 'housing development finance'],
                'patterns': [
                    r'hdfc\s+bank',
                    r'housing\s+development\s+finance',
                    r'www\.hdfcbank\.com',
                    r'\bhdfc\b'
                ],
                'logo_patterns': [
                    r'hdfc',
                    r'housing.*finance'
                ],
                'priority_weight': 1.2
            },
            
            'ICICI': {
                'name': 'ICICI Bank',
                'keywords': ['icici bank', 'icici', 'industrial credit'],
                'patterns': [
                    r'icici\s+bank',
                    r'industrial\s+credit',
                    r'www\.icicibank\.com',
                    r'\bicici\b'
                ],
                'logo_patterns': [
                    r'icici',
                    r'industrial.*credit'
                ],
                'priority_weight': 1.2
            },
            
            'BOB': {
                'name': 'Bank of Baroda (BOB)',
                'keywords': ['bank of baroda', 'bob', 'baroda'],
                'patterns': [
                    r'bank\s+of\s+baroda',
                    r'\bbob\b',
                    r'baroda',
                    r'www\.bankofbaroda\.in'
                ],
                'logo_patterns': [
                    r'baroda',
                    r'bank.*baroda'
                ],
                'priority_weight': 1.1
            },
            
            'KOTAK': {
                'name': 'Kotak Mahindra Bank',
                'keywords': ['kotak mahindra', 'kotak bank', 'kotak'],
                'patterns': [
                    r'kotak\s+mahindra',
                    r'kotak\s+bank',
                    r'www\.kotak\.com',
                    r'\bkotak\b'
                ],
                'logo_patterns': [
                    r'kotak',
                    r'mahindra'
                ],
                'priority_weight': 1.1
            },
            
            'CANARA': {
                'name': 'Canara Bank',
                'keywords': ['canara bank', 'canara'],
                'patterns': [
                    r'canara\s+bank',
                    r'www\.canarabank\.com',
                    r'\bcanara\b'
                ],
                'logo_patterns': [
                    r'canara'
                ],
                'priority_weight': 1.0
            },
            
            'PNB': {
                'name': 'Punjab National Bank (PNB)',
                'keywords': ['punjab national bank', 'pnb', 'punjab national'],
                'patterns': [
                    r'punjab\s+national\s+bank',
                    r'\bpnb\b',
                    r'punjab\s+national',
                    r'www\.pnbindia\.in'
                ],
                'logo_patterns': [
                    r'punjab.*national',
                    r'\bpnb\b'
                ],
                'priority_weight': 1.0
            },
            
            'UBI': {
                'name': 'Union Bank of India (UBI)',
                'keywords': ['union bank of india', 'ubi', 'union bank'],
                'patterns': [
                    r'union\s+bank\s+of\s+india',
                    r'\bubi\b',
                    r'union\s+bank',
                    r'www\.unionbankofindia\.co\.in'
                ],
                'logo_patterns': [
                    r'union.*bank',
                    r'\bubi\b'
                ],
                'priority_weight': 1.0
            },
            
            'BOI': {
                'name': 'Bank of India (BOI)',
                'keywords': ['bank of india', 'boi'],
                'patterns': [
                    r'bank\s+of\s+india',
                    r'\bboi\b',
                    r'www\.bankofindia\.co\.in',
                    r'star\s+connect'
                ],
                'logo_patterns': [
                    r'bank.*india',
                    r'\bboi\b'
                ],
                'priority_weight': 1.0
            },
            
            'IDBI': {
                'name': 'IDBI Bank',
                'keywords': ['idbi bank', 'idbi', 'industrial development bank'],
                'patterns': [
                    r'idbi\s+bank',
                    r'industrial\s+development\s+bank',
                    r'www\.idbibank\.in',
                    r'\bidbi\b'
                ],
                'logo_patterns': [
                    r'idbi',
                    r'industrial.*development'
                ],
                'priority_weight': 1.0
            },
            
            'FEDERAL': {
                'name': 'Federal Bank',
                'keywords': ['federal bank', 'federal'],
                'patterns': [
                    r'federal\s+bank',
                    r'www\.federalbank\.co\.in',
                    r'\bfederal\b'
                ],
                'logo_patterns': [
                    r'federal'
                ],
                'priority_weight': 1.0
            },
            
            'BANDHAN': {
                'name': 'Bandhan Bank',
                'keywords': ['bandhan bank', 'bandhan'],
                'patterns': [
                    r'bandhan\s+bank',
                    r'www\.bandhanbank\.com',
                    r'\bbandhan\b'
                ],
                'logo_patterns': [
                    r'bandhan'
                ],
                'priority_weight': 1.0
            },
            
            'IOB': {
                'name': 'Indian Overseas Bank (IOB)',
                'keywords': ['indian overseas bank', 'iob', 'overseas bank'],
                'patterns': [
                    r'indian\s+overseas\s+bank',
                    r'\biob\b',
                    r'overseas\s+bank',
                    r'www\.iob\.in'
                ],
                'logo_patterns': [
                    r'indian.*overseas',
                    r'\biob\b'
                ],
                'priority_weight': 1.0
            },
            
            'CBI': {
                'name': 'Central Bank of India (CBI)',
                'keywords': ['central bank of india', 'cbi', 'central bank'],
                'patterns': [
                    r'central\s+bank\s+of\s+india',
                    r'\bcbi\b',
                    r'central\s+bank',
                    r'www\.centralbankofindia\.co\.in'
                ],
                'logo_patterns': [
                    r'central.*bank',
                    r'\bcbi\b'
                ],
                'priority_weight': 1.0
            },
            
            'INDIAN': {
                'name': 'Indian Bank',
                'keywords': ['indian bank'],
                'patterns': [
                    r'indian\s+bank(?!\s+of)',
                    r'www\.indianbank\.in'
                ],
                'logo_patterns': [
                    r'indian\s+bank(?!\s+of)'
                ],
                'priority_weight': 1.0
            },
            
            'UCO': {
                'name': 'UCO Bank',
                'keywords': ['uco bank', 'uco'],
                'patterns': [
                    r'uco\s+bank',
                    r'\buco\b',
                    r'www\.ucobank\.com'
                ],
                'logo_patterns': [
                    r'uco'
                ],
                'priority_weight': 1.0
            },
            
            'YES': {
                'name': 'YES Bank',
                'keywords': ['yes bank', 'yes'],
                'patterns': [
                    r'yes\s+bank',
                    r'\byes\b.*bank',
                    r'www\.yesbank\.in',
                    r'\byes\b'
                ],
                'logo_patterns': [
                    r'\byes\b'
                ],
                'priority_weight': 1.0
            },
            
            'SIB': {
                'name': 'South Indian Bank (SIB)',
                'keywords': ['south indian bank', 'sib', 'south indian'],
                'patterns': [
                    r'south\s+indian\s+bank',
                    r'\bsib\b',
                    r'south\s+indian',
                    r'www\.southindianbank\.com'
                ],
                'logo_patterns': [
                    r'south.*indian',
                    r'\bsib\b'
                ],
                'priority_weight': 1.0
            },
            
            'INDUSIND': {
                'name': 'IndusInd Bank',
                'keywords': ['indusind bank', 'indusind'],
                'patterns': [
                    r'indusind\s+bank',
                    r'www\.indusind\.com',
                    r'\bindusind\b'
                ],
                'logo_patterns': [
                    r'indusind'
                ],
                'priority_weight': 1.0
            }
        }

    def extract_text_from_pdf(self, pdf_path: str) -> str:
        """
        从PDF中提取文本内容
        """
        text = ""

        # 方法1: 使用PyPDF2提取文本
        if PYPDF2_AVAILABLE:
            try:
                with open(pdf_path, 'rb') as file:
                    pdf_reader = PyPDF2.PdfReader(file)
                    for page_num in range(min(3, len(pdf_reader.pages))):
                        page = pdf_reader.pages[page_num]
                        text += page.extract_text() + "\n"
                print(f"    📄 PyPDF2提取成功")
            except Exception as e:
                print(f"    ⚠️ PyPDF2提取失败: {e}")

        # 方法2: 使用PyMuPDF提取文本
        if PYMUPDF_AVAILABLE and not text.strip():
            try:
                doc = fitz.open(pdf_path)
                for page_num in range(min(3, len(doc))):
                    page = doc[page_num]
                    text += page.get_text() + "\n"
                doc.close()
                print(f"    📄 PyMuPDF提取成功")
            except Exception as e:
                print(f"    ⚠️ PyMuPDF提取失败: {e}")

        # 方法3: 基于文件名的识别（后备方案）
        if not text.strip():
            filename = os.path.basename(pdf_path).lower()
            text = filename
            print(f"    ℹ️ 使用文件名作为识别依据: {filename}")

        return text.lower()

    def extract_images_from_pdf(self, pdf_path: str) -> List[Image.Image]:
        """
        从PDF中提取图像
        """
        images = []

        if not PYMUPDF_AVAILABLE or not PIL_AVAILABLE:
            print(f"    ⚠️ 图像提取功能不可用")
            return images

        try:
            doc = fitz.open(pdf_path)

            # 只处理前2页以提高性能
            max_pages = min(2, len(doc))

            for page_num in range(max_pages):
                page = doc[page_num]
                image_list = page.get_images()

                for img_index, img in enumerate(image_list):
                    try:
                        # 获取图像数据
                        xref = img[0]
                        pix = fitz.Pixmap(doc, xref)

                        # 转换为PIL Image
                        if pix.n - pix.alpha < 4:  # 确保是RGB或灰度图像
                            img_data = pix.tobytes("ppm")
                            pil_image = Image.open(io.BytesIO(img_data))

                            # 过滤太小的图像（可能不是LOGO）
                            if pil_image.width >= 50 and pil_image.height >= 20:
                                images.append(pil_image)

                        pix = None  # 释放内存

                    except Exception as e:
                        print(f"    ⚠️ 提取图像{img_index}失败: {e}")
                        continue

            doc.close()
            print(f"    📷 从PDF提取了 {len(images)} 个图像")

        except Exception as e:
            print(f"    ❌ PDF图像提取失败: {e}")

        return images

    def ocr_image_text(self, image: Image.Image) -> str:
        """
        使用OCR从图像中提取文本
        """
        if not TESSERACT_AVAILABLE:
            return ""

        try:
            # 图像预处理以提高OCR准确率
            # 转换为灰度图像
            if image.mode != 'L':
                image = image.convert('L')

            # 增强对比度
            enhancer = ImageEnhance.Contrast(image)
            image = enhancer.enhance(2.0)

            # 使用tesseract进行OCR
            text = pytesseract.image_to_string(image, lang='eng+hin')
            return text.lower().strip()

        except Exception as e:
            print(f"    ⚠️ OCR处理失败: {e}")
            return ""

    def calculate_text_score(self, text: str, bank_code: str, bank_info: Dict) -> float:
        """
        计算文本特征匹配分数
        """
        score = 0.0

        # 1. 关键词匹配 (权重: 30%)
        keyword_score = 0
        for keyword in bank_info['keywords']:
            if keyword.lower() in text:
                keyword_score += 1

        if bank_info['keywords']:
            keyword_score = (keyword_score / len(bank_info['keywords'])) * 30

        # 2. 正则模式匹配 (权重: 50%)
        pattern_score = 0
        for pattern in bank_info['patterns']:
            if re.search(pattern, text, re.IGNORECASE):
                pattern_score += 1

        if bank_info['patterns']:
            pattern_score = (pattern_score / len(bank_info['patterns'])) * 50

        # 3. 文件名匹配 (权重: 20%)
        filename = os.path.basename(text).lower()
        filename_score = 0

        # 检查文件名中的银行标识
        bank_indicators = [bank_code.lower()]
        for indicator in bank_indicators:
            if indicator in filename:
                filename_score += 1

        if bank_indicators:
            filename_score = (filename_score / len(bank_indicators)) * 20

        # 基础分数
        base_score = keyword_score + pattern_score + filename_score

        # 应用优先级权重
        final_score = base_score * bank_info.get('priority_weight', 1.0)

        return final_score

    def calculate_image_score(self, images: List[Image.Image], bank_code: str, bank_info: Dict) -> float:
        """
        计算图像LOGO识别分数
        """
        if not images:
            return 0.0

        score = 0.0

        # 合并所有图像的OCR文本
        all_ocr_text = ""
        for i, image in enumerate(images):
            ocr_text = self.ocr_image_text(image)
            if ocr_text:
                all_ocr_text += " " + ocr_text
                print(f"    📝 图像{i+1} OCR结果: {ocr_text[:50]}...")

        if not all_ocr_text.strip():
            print(f"    ⚠️ 未从图像中提取到文本")
            return 0.0

        # 计算LOGO文本匹配分数
        matches = 0
        for pattern in bank_info['logo_patterns']:
            if re.search(pattern, all_ocr_text, re.IGNORECASE):
                matches += 1
                score += 15  # 每个匹配的LOGO模式得15分

        if matches > 0:
            print(f"    🎯 {bank_code}: LOGO匹配 {matches} 个模式，得分 {score}")

        return score

    def identify_bank_multimodal(self, pdf_path: str) -> Tuple[Optional[str], Optional[str], Dict]:
        """
        多模态银行识别
        """
        print(f"  🔍 多模态分析文件: {os.path.basename(pdf_path)}")

        # 1. 文本特征识别
        print(f"    📝 开始文本特征识别...")
        text = self.extract_text_from_pdf(pdf_path)

        text_scores = {}
        if text.strip():
            for bank_code, bank_info in self.bank_patterns.items():
                score = self.calculate_text_score(text, bank_code, bank_info)
                text_scores[bank_code] = score

        # 2. 图像LOGO识别
        print(f"    🖼️ 开始图像LOGO识别...")
        images = self.extract_images_from_pdf(pdf_path)

        image_scores = {}
        for bank_code, bank_info in self.bank_patterns.items():
            score = self.calculate_image_score(images, bank_code, bank_info)
            image_scores[bank_code] = score

        # 3. 多模态融合
        print(f"    🔄 开始多模态融合...")
        final_scores = {}
        all_banks = set(text_scores.keys()) | set(image_scores.keys())

        for bank_code in all_banks:
            text_score = text_scores.get(bank_code, 0)
            image_score = image_scores.get(bank_code, 0)

            # 加权融合：文本权重60%，图像权重40%
            final_score = text_score * 0.6 + image_score * 0.4

            if final_score > 0:
                final_scores[bank_code] = final_score

        # 4. 显示详细分数
        score_details = {}
        for bank_code in all_banks:
            text_score = text_scores.get(bank_code, 0)
            image_score = image_scores.get(bank_code, 0)
            final_score = final_scores.get(bank_code, 0)

            if final_score > 0:
                bank_name = self.bank_patterns[bank_code]['name']
                score_details[bank_code] = {
                    'name': bank_name,
                    'text_score': text_score,
                    'image_score': image_score,
                    'final_score': final_score
                }
                print(f"    📊 {bank_name}: 文本({text_score:.1f}) + 图像({image_score:.1f}) = 最终({final_score:.1f})")

        # 5. 确定最终识别结果
        if final_scores:
            best_bank = max(final_scores.items(), key=lambda x: x[1])
            bank_code, score = best_bank

            # 动态阈值
            if score >= 20.0:
                threshold = 20.0
            elif score >= 15.0:
                threshold = 15.0
            elif score >= 10.0:
                threshold = 10.0
            else:
                threshold = 5.0

            if score >= threshold:
                bank_name = self.bank_patterns[bank_code]['name']
                print(f"    ✅ 识别结果: {bank_name} (最终分数: {score:.1f})")
                return bank_code, bank_name, score_details
            else:
                print(f"    ❌ 分数过低，无法确定银行类型 (最高分: {score:.1f})")
                return None, None, score_details

        print(f"    ❌ 未找到匹配的银行")
        return None, None, score_details

    def scan_directory_full_enhanced(self, directory_path: str) -> List[Dict]:
        """
        完整增强版目录扫描
        """
        results = []
        pdf_files = []

        # 查找所有PDF文件
        for file_path in Path(directory_path).glob("*.pdf"):
            pdf_files.append(str(file_path))

        print(f"📁 找到 {len(pdf_files)} 个PDF文件")
        print("=" * 80)

        # 逐个识别
        for pdf_path in sorted(pdf_files):
            bank_code, bank_name, score_details = self.identify_bank_multimodal(pdf_path)

            result = {
                'file_path': pdf_path,
                'file_name': os.path.basename(pdf_path),
                'bank_code': bank_code,
                'bank_name': bank_name,
                'identified': bank_code is not None,
                'score_details': score_details,
                'final_score': score_details.get(bank_code, {}).get('final_score', 0) if bank_code else 0
            }

            results.append(result)
            print("-" * 80)

        return results

    def print_full_enhanced_summary(self, results: List[Dict]):
        """
        打印完整增强版识别结果摘要
        """
        print("\n" + "=" * 80)
        print("🏦 完整增强版银行PDF文档识别结果汇总")
        print("🔧 集成文本特征识别 + LOGO图像识别的多模态融合方案")
        print("=" * 80)

        identified_count = sum(1 for r in results if r['identified'])
        failed_count = len(results) - identified_count

        print(f"\n📊 统计信息:")
        print(f"  总文件数: {len(results)}")
        print(f"  成功识别: {identified_count}")
        print(f"  识别失败: {failed_count}")
        print(f"  识别率: {(identified_count/len(results)*100):.1f}%")

        # 与之前版本对比
        print(f"\n📈 性能对比:")
        print(f"  原版本识别率: 85.0% (17/20)")
        print(f"  优化版识别率: 100.0% (20/20)")
        print(f"  当前版本识别率: {(identified_count/len(results)*100):.1f}% ({identified_count}/{len(results)})")

        if identified_count == len(results):
            print(f"  🎉 达到100%识别率！")
        elif identified_count > 17:
            improvement = identified_count - 17
            print(f"  🚀 相比原版本提升: +{improvement} 个文件")

        print(f"\n📋 详细结果:")
        for result in results:
            status = "✅" if result['identified'] else "❌"
            bank_info = result['bank_name'] if result['identified'] else "未识别"

            # 显示详细分数信息
            if result['identified'] and result['bank_code'] in result['score_details']:
                details = result['score_details'][result['bank_code']]
                score_info = f"(文本:{details['text_score']:.1f} + 图像:{details['image_score']:.1f} = {details['final_score']:.1f})"
            else:
                score_info = ""

            print(f"  {status} {result['file_name']}")
            print(f"     -> {bank_info} {score_info}")

        # 失败文件分析
        if failed_count > 0:
            print(f"\n🔍 识别失败文件分析:")
            for result in results:
                if not result['identified']:
                    print(f"  ❌ {result['file_name']}")

                    # 显示最高分的银行（即使未达到阈值）
                    if result['score_details']:
                        best_bank = max(result['score_details'].items(),
                                      key=lambda x: x[1]['final_score'])
                        bank_code, details = best_bank
                        print(f"     最接近: {details['name']} (分数: {details['final_score']:.1f})")
                        print(f"     分解: 文本({details['text_score']:.1f}) + 图像({details['image_score']:.1f})")
                    else:
                        print(f"     原因: 无法提取有效特征")

        # 按银行分组统计
        if identified_count > 0:
            bank_counts = {}
            for result in results:
                if result['identified']:
                    bank_name = result['bank_name']
                    bank_counts[bank_name] = bank_counts.get(bank_name, 0) + 1

            print(f"\n🏛️ 银行分布:")
            for bank_name, count in sorted(bank_counts.items()):
                print(f"  {bank_name}: {count} 个文件")

        # 多模态贡献分析
        print(f"\n🔬 多模态贡献分析:")
        text_only_success = 0
        image_helped = 0

        for result in results:
            if result['identified'] and result['bank_code'] in result['score_details']:
                details = result['score_details'][result['bank_code']]
                if details['image_score'] == 0:
                    text_only_success += 1
                else:
                    image_helped += 1

        print(f"  仅文本识别成功: {text_only_success} 个")
        print(f"  图像识别辅助成功: {image_helped} 个")

        if image_helped > 0:
            print(f"  🎯 图像识别贡献率: {(image_helped/identified_count*100):.1f}%")
        else:
            print(f"  ℹ️ 当前主要依赖文本识别")


def main():
    """主函数"""
    print("🚀 启动完整增强版银行PDF文档自动识别系统")
    print("🎯 目标目录: bank_files")
    print("🔧 集成文本特征识别 + LOGO图像识别的多模态融合方案")
    print("=" * 80)

    # 目标目录
    target_directory = "bank_files"

    if not os.path.exists(target_directory):
        print(f"❌ 目录不存在: {target_directory}")
        return

    # 创建完整增强版识别器
    identifier = FullEnhancedBankPDFIdentifier()

    # 扫描并识别
    results = identifier.scan_directory_full_enhanced(target_directory)

    # 打印结果摘要
    identifier.print_full_enhanced_summary(results)

    print(f"\n🎉 完整增强版银行PDF识别完成！")


if __name__ == "__main__":
    main()
