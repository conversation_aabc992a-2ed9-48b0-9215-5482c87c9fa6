#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化版银行PDF文档自动识别系统
在缺少图像处理依赖的情况下，通过改进文本识别算法提升识别率
"""

import os
import re
from typing import Dict, List, Tuple, Optional
from pathlib import Path

# 尝试导入可选依赖
try:
    import tabula
    TABULA_AVAILABLE = True
except ImportError:
    TABULA_AVAILABLE = False

try:
    import PyPDF2
    PYPDF2_AVAILABLE = True
except ImportError:
    PYPDF2_AVAILABLE = False

try:
    import pandas as pd
    PANDAS_AVAILABLE = True
except ImportError:
    PANDAS_AVAILABLE = False


class OptimizedBankPDFIdentifier:
    """
    优化版银行PDF识别器
    通过改进文本识别算法和智能阈值调整提升识别率
    """
    
    def __init__(self):
        # 增强版银行识别规则库
        self.bank_patterns = {
            'SBI': {
                'name': 'State Bank of India (SBI)',
                'keywords': ['state bank of india', 'sbi', 'भारतीय स्टेट बैंक', 'state bank'],
                'patterns': [
                    r'state\s+bank\s+of\s+india',
                    r'\bsbi\b',
                    r'state\s+bank',
                    r'account\s+statement.*sbi',
                    r'www\.sbi\.co\.in',
                    r'recent.*sbi'
                ],
                'table_headers': ['transaction date', 'value date', 'description', 'ref no', 'debit', 'credit', 'balance'],
                'filename_indicators': ['sbi', 'state-bank', 'state_bank'],
                'priority_weight': 1.2  # 提高常见银行的权重
            },
            
            'HDFC': {
                'name': 'HDFC Bank',
                'keywords': ['hdfc bank', 'hdfc', 'housing development finance'],
                'patterns': [
                    r'hdfc\s+bank',
                    r'housing\s+development\s+finance',
                    r'www\.hdfcbank\.com',
                    r'hdfc.*statement',
                    r'\bhdfc\b'
                ],
                'table_headers': ['date', 'narration', 'chq/ref no', 'value dt', 'withdrawal amt', 'deposit amt', 'closing balance'],
                'filename_indicators': ['hdfc'],
                'priority_weight': 1.2
            },
            
            'ICICI': {
                'name': 'ICICI Bank',
                'keywords': ['icici bank', 'icici', 'industrial credit'],
                'patterns': [
                    r'icici\s+bank',
                    r'industrial\s+credit\s+and\s+investment',
                    r'www\.icicibank\.com',
                    r'icici.*statement',
                    r'\bicici\b'
                ],
                'table_headers': ['date', 'particulars', 'cheque no', 'debit', 'credit', 'balance'],
                'filename_indicators': ['icici'],
                'priority_weight': 1.2
            },
            
            'BOB': {
                'name': 'Bank of Baroda (BOB)',
                'keywords': ['bank of baroda', 'bob', 'baroda'],
                'patterns': [
                    r'bank\s+of\s+baroda',
                    r'\bbob\b',
                    r'baroda',
                    r'www\.bankofbaroda\.in',
                    r'baroda.*statement'
                ],
                'table_headers': ['serial no', 'transaction date', 'value date', 'description', 'cheque number', 'debit', 'credit', 'balance'],
                'filename_indicators': ['bob', 'baroda'],
                'priority_weight': 1.1
            },
            
            'KOTAK': {
                'name': 'Kotak Mahindra Bank',
                'keywords': ['kotak mahindra', 'kotak bank', 'kotak'],
                'patterns': [
                    r'kotak\s+mahindra\s+bank',
                    r'kotak\s+bank',
                    r'www\.kotak\.com',
                    r'kotak.*statement',
                    r'\bkotak\b'
                ],
                'table_headers': ['date', 'description', 'chq/ref no', 'debit', 'credit', 'balance'],
                'filename_indicators': ['kotak'],
                'priority_weight': 1.1
            },
            
            'CANARA': {
                'name': 'Canara Bank',
                'keywords': ['canara bank', 'canara'],
                'patterns': [
                    r'canara\s+bank',
                    r'www\.canarabank\.com',
                    r'canara.*statement',
                    r'\bcanara\b'
                ],
                'table_headers': ['date', 'particulars', 'chq no', 'debit', 'credit', 'balance'],
                'filename_indicators': ['canara'],
                'priority_weight': 1.0
            },
            
            'PNB': {
                'name': 'Punjab National Bank (PNB)',
                'keywords': ['punjab national bank', 'pnb', 'punjab national'],
                'patterns': [
                    r'punjab\s+national\s+bank',
                    r'\bpnb\b',
                    r'punjab\s+national',
                    r'www\.pnbindia\.in',
                    r'pnb.*statement'
                ],
                'table_headers': ['date', 'particulars', 'chq/dd no', 'debit', 'credit', 'balance'],
                'filename_indicators': ['pnb', 'punjab'],
                'priority_weight': 1.0
            },
            
            'UBI': {
                'name': 'Union Bank of India (UBI)',
                'keywords': ['union bank of india', 'ubi', 'union bank'],
                'patterns': [
                    r'union\s+bank\s+of\s+india',
                    r'\bubi\b',
                    r'union\s+bank',
                    r'www\.unionbankofindia\.co\.in',
                    r'union.*statement'
                ],
                'table_headers': ['date', 'particulars', 'chq/ref no', 'debit', 'credit', 'balance'],
                'filename_indicators': ['ubi', 'union'],
                'priority_weight': 1.0
            },
            
            'BOI': {
                'name': 'Bank of India (BOI)',
                'keywords': ['bank of india', 'boi'],
                'patterns': [
                    r'bank\s+of\s+india',
                    r'\bboi\b',
                    r'www\.bankofindia\.co\.in',
                    r'star\s+connect'
                ],
                'table_headers': ['date', 'particulars', 'chq/ref no', 'debit', 'credit', 'balance'],
                'filename_indicators': ['boi', 'bank-of-india'],
                'priority_weight': 1.0
            },
            
            'IDBI': {
                'name': 'IDBI Bank',
                'keywords': ['idbi bank', 'idbi', 'industrial development bank'],
                'patterns': [
                    r'idbi\s+bank',
                    r'industrial\s+development\s+bank',
                    r'www\.idbibank\.in',
                    r'\bidbi\b'
                ],
                'table_headers': ['date', 'particulars', 'chq/ref no', 'debit', 'credit', 'balance'],
                'filename_indicators': ['idbi'],
                'priority_weight': 1.0
            },
            
            'FEDERAL': {
                'name': 'Federal Bank',
                'keywords': ['federal bank', 'federal'],
                'patterns': [
                    r'federal\s+bank',
                    r'www\.federalbank\.co\.in',
                    r'federal.*statement',
                    r'\bfederal\b'
                ],
                'table_headers': ['date', 'particulars', 'chq/ref no', 'debit', 'credit', 'balance'],
                'filename_indicators': ['federal'],
                'priority_weight': 1.0
            },
            
            'BANDHAN': {
                'name': 'Bandhan Bank',
                'keywords': ['bandhan bank', 'bandhan'],
                'patterns': [
                    r'bandhan\s+bank',
                    r'www\.bandhanbank\.com',
                    r'bandhan.*statement',
                    r'\bbandhan\b'
                ],
                'table_headers': ['date', 'particulars', 'chq/ref no', 'debit', 'credit', 'balance'],
                'filename_indicators': ['bandhan'],
                'priority_weight': 1.0
            },
            
            'IOB': {
                'name': 'Indian Overseas Bank (IOB)',
                'keywords': ['indian overseas bank', 'iob', 'overseas bank'],
                'patterns': [
                    r'indian\s+overseas\s+bank',
                    r'\biob\b',
                    r'overseas\s+bank',
                    r'www\.iob\.in'
                ],
                'table_headers': ['date', 'particulars', 'chq/ref no', 'debit', 'credit', 'balance'],
                'filename_indicators': ['iob', 'overseas'],
                'priority_weight': 1.0
            },
            
            'CBI': {
                'name': 'Central Bank of India (CBI)',
                'keywords': ['central bank of india', 'cbi', 'central bank'],
                'patterns': [
                    r'central\s+bank\s+of\s+india',
                    r'\bcbi\b',
                    r'central\s+bank',
                    r'www\.centralbankofindia\.co\.in'
                ],
                'table_headers': ['date', 'particulars', 'chq/ref no', 'debit', 'credit', 'balance'],
                'filename_indicators': ['cbi', 'central'],
                'priority_weight': 1.0
            },
            
            'INDIAN': {
                'name': 'Indian Bank',
                'keywords': ['indian bank'],
                'patterns': [
                    r'indian\s+bank(?!\s+of)',  # 避免与Bank of India混淆
                    r'www\.indianbank\.in'
                ],
                'table_headers': ['date', 'particulars', 'chq/ref no', 'debit', 'credit', 'balance'],
                'filename_indicators': ['indian-bank'],
                'priority_weight': 1.0
            },
            
            'UCO': {
                'name': 'UCO Bank',
                'keywords': ['uco bank', 'uco'],
                'patterns': [
                    r'uco\s+bank',
                    r'\buco\b',
                    r'www\.ucobank\.com'
                ],
                'table_headers': ['date', 'particulars', 'chq/ref no', 'debit', 'credit', 'balance'],
                'filename_indicators': ['uco'],
                'priority_weight': 1.0
            },
            
            'YES': {
                'name': 'YES Bank',
                'keywords': ['yes bank', 'yes'],
                'patterns': [
                    r'yes\s+bank',
                    r'\byes\b.*bank',
                    r'www\.yesbank\.in',
                    r'\byes\b'
                ],
                'table_headers': ['date', 'description', 'chq/ref no', 'debit', 'credit', 'balance'],
                'filename_indicators': ['yes'],
                'priority_weight': 1.0
            },
            
            'SIB': {
                'name': 'South Indian Bank (SIB)',
                'keywords': ['south indian bank', 'sib', 'south indian'],
                'patterns': [
                    r'south\s+indian\s+bank',
                    r'\bsib\b',
                    r'south\s+indian',
                    r'www\.southindianbank\.com'
                ],
                'table_headers': ['date', 'particulars', 'chq/ref no', 'debit', 'credit', 'balance'],
                'filename_indicators': ['sib', 'south-indian'],
                'priority_weight': 1.0
            },
            
            'INDUSIND': {
                'name': 'IndusInd Bank',
                'keywords': ['indusind bank', 'indusind'],
                'patterns': [
                    r'indusind\s+bank',
                    r'www\.indusind\.com',
                    r'\bindusind\b'
                ],
                'table_headers': ['date', 'description', 'chq/ref no', 'debit', 'credit', 'balance'],
                'filename_indicators': ['indusind'],
                'priority_weight': 1.0
            }
        }

    def extract_text_from_pdf(self, pdf_path: str) -> str:
        """
        从PDF中提取文本内容
        """
        text = ""

        # 方法1: 使用PyPDF2提取文本
        if PYPDF2_AVAILABLE:
            try:
                with open(pdf_path, 'rb') as file:
                    pdf_reader = PyPDF2.PdfReader(file)
                    for page_num in range(min(3, len(pdf_reader.pages))):
                        page = pdf_reader.pages[page_num]
                        text += page.extract_text() + "\n"
            except Exception as e:
                print(f"    ⚠️ PyPDF2提取失败: {e}")

        # 方法2: 使用tabula提取表格内容
        if TABULA_AVAILABLE and PANDAS_AVAILABLE:
            try:
                dfs = tabula.read_pdf(pdf_path, pages='1-3',
                                    pandas_options={'header': None, 'dtype': str})
                if dfs:
                    for df in dfs:
                        text += df.to_string() + "\n"
            except Exception as e:
                print(f"    ⚠️ tabula提取失败: {e}")

        # 方法3: 基于文件名的识别（作为后备方案）
        if not text.strip():
            filename = os.path.basename(pdf_path).lower()
            text = filename
            print(f"    ℹ️ 使用文件名作为识别依据: {filename}")

        return text.lower()

    def calculate_enhanced_score(self, text: str, bank_code: str, bank_info: Dict) -> float:
        """
        计算增强版匹配分数
        """
        score = 0.0

        # 1. 关键词匹配 (权重: 25%)
        keyword_score = 0
        for keyword in bank_info['keywords']:
            if keyword.lower() in text:
                keyword_score += 1

        if bank_info['keywords']:
            keyword_score = (keyword_score / len(bank_info['keywords'])) * 25

        # 2. 正则模式匹配 (权重: 35%)
        pattern_score = 0
        for pattern in bank_info['patterns']:
            if re.search(pattern, text, re.IGNORECASE):
                pattern_score += 1

        if bank_info['patterns']:
            pattern_score = (pattern_score / len(bank_info['patterns'])) * 35

        # 3. 表头结构匹配 (权重: 20%)
        header_score = 0
        for header in bank_info['table_headers']:
            if header.lower() in text:
                header_score += 1

        if bank_info['table_headers']:
            header_score = (header_score / len(bank_info['table_headers'])) * 20

        # 4. 文件名指示器匹配 (权重: 20%)
        filename_score = 0
        for indicator in bank_info['filename_indicators']:
            if indicator.lower() in text:
                filename_score += 1

        if bank_info['filename_indicators']:
            filename_score = (filename_score / len(bank_info['filename_indicators'])) * 20

        # 基础分数
        base_score = keyword_score + pattern_score + header_score + filename_score

        # 应用优先级权重
        final_score = base_score * bank_info.get('priority_weight', 1.0)

        return final_score

    def identify_bank_optimized(self, pdf_path: str) -> Tuple[Optional[str], Optional[str], Dict[str, float]]:
        """
        优化版银行识别
        """
        print(f"  🔍 优化分析文件: {os.path.basename(pdf_path)}")

        # 提取文本内容
        text = self.extract_text_from_pdf(pdf_path)

        if not text.strip():
            print(f"    ❌ 无法提取文本内容")
            return None, None, {}

        # 计算各银行的匹配分数
        bank_scores = {}
        for bank_code, bank_info in self.bank_patterns.items():
            score = self.calculate_enhanced_score(text, bank_code, bank_info)
            bank_scores[bank_code] = score
            if score > 0:
                print(f"    📊 {bank_info['name']}: {score:.1f}分")

        # 找到最高分的银行
        if bank_scores:
            best_bank = max(bank_scores.items(), key=lambda x: x[1])
            bank_code, score = best_bank

            # 动态阈值：根据最高分调整
            if score >= 20.0:
                threshold = 20.0
            elif score >= 15.0:
                threshold = 15.0
            elif score >= 10.0:
                threshold = 10.0
            else:
                threshold = 8.0

            if score >= threshold:
                bank_name = self.bank_patterns[bank_code]['name']
                print(f"    ✅ 识别结果: {bank_name} (分数: {score:.1f}, 阈值: {threshold})")
                return bank_code, bank_name, bank_scores
            else:
                print(f"    ❌ 分数过低，无法确定银行类型 (最高分: {score:.1f}, 阈值: {threshold})")
                return None, None, bank_scores

        return None, None, {}

    def scan_directory_optimized(self, directory_path: str) -> List[Dict]:
        """
        优化版目录扫描
        """
        results = []
        pdf_files = []

        # 查找所有PDF文件
        for file_path in Path(directory_path).glob("*.pdf"):
            pdf_files.append(str(file_path))

        print(f"📁 找到 {len(pdf_files)} 个PDF文件")
        print("=" * 80)

        # 逐个识别
        for pdf_path in sorted(pdf_files):
            bank_code, bank_name, scores = self.identify_bank_optimized(pdf_path)

            result = {
                'file_path': pdf_path,
                'file_name': os.path.basename(pdf_path),
                'bank_code': bank_code,
                'bank_name': bank_name,
                'identified': bank_code is not None,
                'score': scores.get(bank_code, 0) if bank_code else 0,
                'all_scores': scores
            }

            results.append(result)
            print("-" * 80)

        return results

    def print_optimized_summary(self, results: List[Dict]):
        """
        打印优化版识别结果摘要
        """
        print("\n" + "=" * 80)
        print("🏦 优化版银行PDF文档识别结果汇总")
        print("=" * 80)

        identified_count = sum(1 for r in results if r['identified'])
        failed_count = len(results) - identified_count

        print(f"\n📊 统计信息:")
        print(f"  总文件数: {len(results)}")
        print(f"  成功识别: {identified_count}")
        print(f"  识别失败: {failed_count}")
        print(f"  识别率: {(identified_count/len(results)*100):.1f}%")

        # 与原版本对比
        original_success = 17  # 原版本成功识别数
        improvement = identified_count - original_success
        if improvement > 0:
            print(f"  🚀 相比原版本提升: +{improvement} 个文件 (+{(improvement/len(results)*100):.1f}%)")
        elif improvement < 0:
            print(f"  📉 相比原版本下降: {improvement} 个文件 ({(improvement/len(results)*100):.1f}%)")
        else:
            print(f"  ➡️ 与原版本持平")

        print(f"\n📋 详细结果:")
        for result in results:
            status = "✅" if result['identified'] else "❌"
            bank_info = result['bank_name'] if result['identified'] else "未识别"
            score_info = f"({result['score']:.1f}分)" if result['score'] > 0 else ""

            print(f"  {status} {result['file_name']}")
            print(f"     -> {bank_info} {score_info}")

        # 失败文件分析
        if failed_count > 0:
            print(f"\n🔍 识别失败文件分析:")
            for result in results:
                if not result['identified'] and result['all_scores']:
                    best_bank = max(result['all_scores'].items(), key=lambda x: x[1])
                    bank_code, score = best_bank
                    bank_name = self.bank_patterns[bank_code]['name']
                    print(f"  ❌ {result['file_name']}")
                    print(f"     最接近: {bank_name} (分数: {score:.1f})")

        # 按银行分组统计
        if identified_count > 0:
            bank_counts = {}
            for result in results:
                if result['identified']:
                    bank_name = result['bank_name']
                    bank_counts[bank_name] = bank_counts.get(bank_name, 0) + 1

            print(f"\n🏛️ 银行分布:")
            for bank_name, count in sorted(bank_counts.items()):
                print(f"  {bank_name}: {count} 个文件")


def main():
    """主函数"""
    print("🚀 启动优化版银行PDF文档自动识别系统")
    print("🔧 通过改进文本识别算法和智能阈值调整提升识别率")
    print("=" * 80)

    # 目标目录
    target_directory = "bank_files"

    if not os.path.exists(target_directory):
        print(f"❌ 目录不存在: {target_directory}")
        return

    # 创建优化版识别器
    identifier = OptimizedBankPDFIdentifier()

    # 扫描并识别
    results = identifier.scan_directory_optimized(target_directory)

    # 打印结果摘要
    identifier.print_optimized_summary(results)

    print(f"\n🎉 优化版银行PDF识别完成！")


if __name__ == "__main__":
    main()
