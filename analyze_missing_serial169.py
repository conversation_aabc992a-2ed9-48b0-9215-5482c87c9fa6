#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析Serial 169描述缺失问题
"""

import pandas as pd
import tabula

def analyze_serial_169():
    """分析Serial 169的描述缺失问题"""
    pdf_path = "../files/8-bob-623136717-BOB-STATEMENT.pdf"
    
    print('=' * 80)
    print('分析Serial 169描述缺失问题')
    print('=' * 80)
    
    try:
        # 解析第7页（Serial 169应该在这里）
        dfs = tabula.read_pdf(pdf_path, pages='7', 
                            stream=True,
                            pandas_options={'header': None, 'dtype': str})
        
        if dfs:
            df = dfs[0]
            print(f"第7页原始表格形状: {df.shape}")
            
            # 找到Serial 169的位置
            serial_169_pos = None
            for i, row in df.iterrows():
                serial_no = str(row.iloc[0]).strip() if len(row) > 0 else ""
                if serial_no == '169':
                    serial_169_pos = i
                    break
            
            if serial_169_pos is not None:
                print(f"\n📍 找到Serial 169在行{serial_169_pos}")
                
                # 分析Serial 169前后的行
                print(f"\nSerial 169前后行分析（±5行）:")
                start_range = max(0, serial_169_pos - 5)
                end_range = min(len(df), serial_169_pos + 6)
                
                for i in range(start_range, end_range):
                    if i < len(df):
                        row = df.iloc[i]
                        serial = str(row.iloc[0]).strip() if len(row) > 0 else ""
                        desc = str(row.iloc[3]).strip() if len(row) > 3 else ""
                        
                        marker = "👉" if i == serial_169_pos else "  "
                        serial_info = f"Serial={serial}" if serial != 'nan' else "Serial=nan"
                        desc_info = f"Desc='{desc}'" if desc != 'nan' else "Desc=nan"
                        
                        print(f"    {marker} 行{i}: {serial_info}, {desc_info}")
                
                # 检查Serial 169行的完整内容
                serial_169_row = df.iloc[serial_169_pos]
                print(f"\n📋 Serial 169行完整内容:")
                for col_idx, value in enumerate(serial_169_row):
                    print(f"    列{col_idx}: '{str(value).strip()}'")
                
                # 分析为什么当前算法没有找到描述
                print(f"\n🔍 当前算法分析:")
                print(f"当前算法查找范围: ±3行")
                
                # 在±3行范围内查找描述
                nearby_descriptions = []
                for j in range(max(0, serial_169_pos - 3), min(len(df), serial_169_pos + 4)):
                    if j != serial_169_pos and j < len(df):
                        row = df.iloc[j]
                        desc = str(row.iloc[3]).strip() if len(row) > 3 else ""
                        if desc and desc.lower() != 'nan':
                            nearby_descriptions.append((j, desc))
                
                if nearby_descriptions:
                    print(f"算法范围内找到 {len(nearby_descriptions)} 个描述:")
                    for pos, desc in nearby_descriptions:
                        print(f"  行{pos}: '{desc}'")
                        
                        # 检查是否符合当前硬编码关键字
                        has_keywords = any(keyword in desc for keyword in ['UPI/', 'IMPS/', 'MBK/', 'NEFT/', 'RTGS/'])
                        is_short = len(desc) <= 3
                        
                        print(f"    关键字匹配: {has_keywords}, 短字符: {is_short}")
                        
                        if not has_keywords and not is_short:
                            print(f"    ❌ 被当前算法忽略（不符合硬编码规则）")
                        else:
                            print(f"    ✅ 符合当前算法规则")
                else:
                    print(f"算法范围内未找到描述")
                
                # 扩大搜索范围
                print(f"\n🔍 扩大搜索范围分析（±10行）:")
                extended_descriptions = []
                for j in range(max(0, serial_169_pos - 10), min(len(df), serial_169_pos + 11)):
                    if j != serial_169_pos and j < len(df):
                        row = df.iloc[j]
                        desc = str(row.iloc[3]).strip() if len(row) > 3 else ""
                        if desc and desc.lower() != 'nan':
                            distance = abs(j - serial_169_pos)
                            extended_descriptions.append((j, desc, distance))
                
                if extended_descriptions:
                    print(f"扩大范围找到 {len(extended_descriptions)} 个描述:")
                    for pos, desc, distance in sorted(extended_descriptions, key=lambda x: x[2]):
                        print(f"  行{pos} (距离{distance}): '{desc}'")
            else:
                print(f"❌ 未找到Serial 169")
    
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    analyze_serial_169()
